<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Reports extends MY_Controller
{

    function __construct() {
        parent::__construct();


        if ( ! $this->loggedIn) {
            redirect('login');
        }

        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }

        $this->load->model('reports_model');
    }

    function ConvertDateTime($dataBR){

        if(empty($dataBR)) return "";

        $d1 = explode(" ", $dataBR);
        $dias = $d1[0];
        $dias = explode("/", $dias);

        $dias = $dias[2]."-".$dias[1]."-".$dias[0];
        $horas = ($d1[1])? $d1[1] : "";

        return trim($dias." ".$horas);

    }

    function daily_sales($year = NULL, $month = NULL)
    {
        if (!$year) { $year = date('Y'); }
        if (!$month) { $month = date('m'); }
        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->lang->load('calendar');
        $config = array(
            'show_next_prev' => TRUE,
            'next_prev_url' => site_url('reports/daily_sales'),
            'month_type' => 'long',
            'day_type' => 'long'
            );
        $config['template'] = '

        {table_open}<table border="0" cellpadding="0" cellspacing="0" class="table table-bordered" style="min-width:522px;">{/table_open}

        {heading_row_start}<tr class="active">{/heading_row_start}

        {heading_previous_cell}<th><div class="text-center"><a href="{previous_url}">&lt;&lt;</div></a></th>{/heading_previous_cell}
        {heading_title_cell}<th colspan="{colspan}"><div class="text-center">{heading}</div></th>{/heading_title_cell}
        {heading_next_cell}<th><div class="text-center"><a href="{next_url}">&gt;&gt;</a></div></th>{/heading_next_cell}

        {heading_row_end}</tr>{/heading_row_end}

        {week_row_start}<tr>{/week_row_start}
        {week_day_cell}<td class="cl_equal"><div class="cl_wday">{week_day}</div></td>{/week_day_cell}
        {week_row_end}</tr>{/week_row_end}

        {cal_row_start}<tr>{/cal_row_start}
        {cal_cell_start}<td>{/cal_cell_start}

        {cal_cell_content}<div class="cl_left">{day}</div><div class="cl_center">{content}</div>{/cal_cell_content}
        {cal_cell_content_today}<div class="cl_left highlight">{day}</div><div class="cl_center">{content}</div>{/cal_cell_content_today}

        {cal_cell_no_content}{day}{/cal_cell_no_content}
        {cal_cell_no_content_today}<div class="highlight">{day}</div>{/cal_cell_no_content_today}

        {cal_cell_blank}&nbsp;{/cal_cell_blank}

        {cal_cell_end}</td>{/cal_cell_end}
        {cal_row_end}</tr>{/cal_row_end}

        {table_close}</table>{/table_close}
        ';

        $this->load->library('calendar', $config);

        $sales = $this->reports_model->getDailySales($year, $month);

        if(!empty($sales)) {
            foreach($sales as $sale){
                $daily_sale[$sale->date] = "<span class='daily-sales-total'>".$this->tec->formatMoney($sale->grand_total)."</span>";
            }
        } else {
            $daily_sale = array();
        }

        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['calender'] = $this->calendar->generate($year, $month, $daily_sale);

        $start = $year.'-'.$month.'-01 00:00:00';
        $end = $year.'-'.$month.'-'.days_in_month($month, $year).' 23:59:59';
        $this->data['total_purchases'] = $this->reports_model->getTotalPurchases($start, $end);
        $this->data['total_sales'] = $this->reports_model->getTotalSales($start, $end);
        $this->data['total_expenses'] = $this->reports_model->getTotalExpenses($start, $end);

        $this->data['page_title'] = $this->lang->line("daily_sales");
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('daily_sales')));
        $meta = array('page_title' => lang('daily_sales'), 'bc' => $bc);
        $this->page_construct('reports/daily', $this->data, $meta);

    }


    function monthly_sales($year = NULL)
    {
        if(!$year) { $year = date('Y'); }
        $this->lang->load('calendar');
        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $start = $year.'-01-01 00:00:00';
        $end = $year.'-12-31 23:59:59';
        $this->data['total_purchases'] = $this->reports_model->getTotalPurchases($start, $end);
        $this->data['total_sales'] = $this->reports_model->getTotalSales($start, $end);
        $this->data['total_expenses'] = $this->reports_model->getTotalExpenses($start, $end);
        $this->data['year'] = $year;
        $this->data['sales'] = $this->reports_model->getMonthlySales($year);

        // Fetch sale items with costs
        $this->data['sale_items'] = $this->reports_model->getMonthlySaleItems($start, $end);

        // Fetch monthly expenses
        $this->data['expenses'] = $this->reports_model->getMonthlyExpenses($start, $end);

        // Fetch monthly payments by type
        $this->data['monthly_payments'] = $this->reports_model->getMonthlyPaymentsByType($year);

        $this->data['page_title'] = $this->lang->line("monthly_sales");
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('monthly_sales')));
        $meta = array('page_title' => lang('monthly_sales'), 'bc' => $bc);
        $this->page_construct('reports/monthly', $this->data, $meta);
    }

    function index()
    {

        $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
        if($this->input->get('customer')) {
            $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : NULL;
            $end_date = $this->input->get('end_date') ? $this->input->get('end_date') : NULL;
            $user = $this->input->get('user') ? $this->input->get('user') : NULL;
            $this->data['total_sales'] = $this->reports_model->getTotalSalesforCustomer($this->input->get('customer'), $user, $start_date, $end_date);
            $totasales = $this->reports_model->getTotalSalesValueforCustomer($this->input->get('customer'), $user, $start_date, $end_date);
            $this->data['total_sales_value'] = ($totasales!="")? number_format((float)$totasales, 2, ',', '.') : "0,00";
        }
        $this->data['isAdmin'] = $this->Admin;
        $this->data['customers'] = $this->reports_model->getAllCustomers();
        $this->data['users'] = $this->reports_model->getAllStaff();
        $this->data['page_title'] = $this->lang->line("sales_report");

        if ($this->input->get('tipo')=="contas_a_receber") {
            $this->data['page_title'] .= " - Contas a receber";
        }

        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('sales_report')));
        $meta = array('page_title' =>  $this->data['page_title'], 'bc' => $bc);
        $this->page_construct('reports/sales', $this->data, $meta);
    }

    function get_sales()
    {

        $this->load->library('datatables');
        $this->datatables
        ->select(" CONCAT('<a href=".site_url('pos/view')."/', ".$this->db->dbprefix('sales').".id,'?from=sales_list class=linkcustomer_1 target=_blank title=Venda alt=Venda>', ".$this->db->dbprefix('sales').".id,'</a>', '<span class=linkcustomer_2 style=display:none;>',  ".$this->db->dbprefix('sales').".id ,'</span>') as idsale, sales.date, CONCAT('<a href=reports?customer=', customer_id, ' class=linkcustomer_1 alt=Vendas>', COALESCE(NULLIF(customer_name, ''), tec_customers.cf1, '*Desconhecido*'), '</a>', '<span class=linkcustomer_2 style=display:none;>', COALESCE(NULLIF(customer_name, ''), tec_customers.cf1, '*Desconhecido*'),'</span>') as customer_name, total, total_tax, total_discount, grand_total, (paid - troco) as paidtotal, CASE WHEN (paid - grand_total) > 0 then 0 ELSE (paid - grand_total) END as balance, troco, sales.status")
        ->join('tec_customers', 'tec_customers.id=customer_id', 'left')
        ->from('sales');

        $customer = $this->input->get('customer') ? $this->input->get('customer') : NULL;
        $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : NULL;
        $end_date = $this->input->get('end_date') ? $this->input->get('end_date') : NULL;
        $user = $this->input->get('user') ? $this->input->get('user') : NULL;
        $ispaid = $this->input->get('ispaid') ? $this->input->get('ispaid') : NULL;
        //$paid_by = $this->input->get('paid_by') ? $this->input->get('paid_by') : NULL;
        //$vendedor = $this->input->get('vendedor') ? $this->input->get('vendedor') : NULL;
        // filtros
        if($customer!=0) { $this->datatables->where('customer_id', $customer); }
        if($start_date!="") { $this->datatables->where('date >=', $this->tec->FormatarDataTimeBRParaDB($start_date));  } // formatar data
        if($end_date!="") { $this->datatables->where('date <=', $this->tec->FormatarDataTimeBRParaDB($end_date)); } // formatar data
        // 1- so pagos, 2 - só não pagos
        if($ispaid == "1") {  $this->datatables->where('status', "Pago"); }
        if($ispaid == "2") {  $this->datatables->where('status !=', "Pago"); }
        if($ispaid == "3") {  $this->datatables->where('status', "Parcial"); }
        if($ispaid == "4") {  $this->datatables->where('status', "Não pago"); }
        if(!$this->Admin) {
            $user_id = $this->session->userdata('user_id');
            $this->datatables->where('created_by', $user_id);
        }else{
            if($user!=0){
                $this->datatables->where('created_by', $user);
                $this->datatables->or_where('vendedor', $user);
            }
        }

        echo $this->datatables->generate();

    }

    function products()
    {

        $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));

        $this->data['products'] = $this->reports_model->getAllProducts();
        $this->data['page_title'] = $this->lang->line("products_report");
        $this->data['page_title'] = $this->lang->line("products_report");
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('products_report')));
        $meta = array('page_title' => lang('products_report'), 'bc' => $bc);
        $this->page_construct('reports/products', $this->data, $meta);
    }

    function get_products()
    {
        $product = $this->input->get('product') ? $this->input->get('product') : NULL;
        $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : NULL;
        $end_date = $this->input->get('end_date') ? $this->input->get('end_date') : NULL;

        //COALESCE(sum(".$this->db->dbprefix('sale_items').".quantity)*".$this->db->dbprefix('products').".cost, 0) as cost,
        $this->load->library('datatables');

        $this->datatables
        ->select(" ".$this->db->dbprefix('products').".code,  CONCAT('<a class=linkcustomer_1 href=".site_url('products/edit/')."/', ".$this->db->dbprefix('products').".id,' class=tip target=_blank title=Ver/Editar alt=Editar>', ".$this->db->dbprefix('products').".name,'</a>', '<span class=linkcustomer_2 style=display:none;>',  ".$this->db->dbprefix('products').".name ,'</span>') as prodname, COALESCE(sum(".$this->db->dbprefix('sale_items').".quantity), 0) as sold, COALESCE(sum(".$this->db->dbprefix('sale_items').".quantity)*".$this->db->dbprefix('sale_items').".cost, 0) as cost, COALESCE(sum(".$this->db->dbprefix('sale_items').".subtotal), 0) as income,
            ROUND((COALESCE(sum(".$this->db->dbprefix('sale_items').".subtotal), 0)) - COALESCE(sum(".$this->db->dbprefix('sale_items').".quantity)*".$this->db->dbprefix('sale_items').".cost, 0) -COALESCE(((sum(".$this->db->dbprefix('sale_items').".subtotal)*".$this->db->dbprefix('products').".tax)/100), 0), 2)
            as profit", FALSE)
        ->from('sale_items')
        ->where($this->db->dbprefix('products').".code is NOT NULL", NULL, FALSE)
        ->join('products', 'sale_items.product_id=products.id', 'left' )
        ->join('sales', 'sale_items.sale_id=sales.id', 'left' )
        ->group_by('products.id');

        if($product) { $this->datatables->where('products.id', $product); }
        if($start_date) { $this->datatables->where('date >=',  $this->ConvertDateTime($start_date)); }
        if($end_date) { $this->datatables->where('date <=',  $this->ConvertDateTime($end_date)); }

        echo $this->datatables->generate();
    }



    function profit( $income, $cost, $tax)
    {
        return floatval($income)." - ".floatval($cost)." - ".floatval($tax);
    }

    function top_products()
    {

        $this->data['topProducts'] = $this->reports_model->topProducts();
        $this->data['topProducts1'] = $this->reports_model->topProducts1();
        $this->data['topProducts3'] = $this->reports_model->topProducts3();
        $this->data['topProducts12'] = $this->reports_model->topProducts12();

        $this->data['page_title'] = $this->lang->line("top_products");
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('top_products')));
        $meta = array('page_title' => lang('top_products'), 'bc' => $bc);
        $this->page_construct('reports/top', $this->data, $meta);
    }

    function registers()
    {

        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['users'] = $this->reports_model->getAllStaff();
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('registers_report')));
        $meta = array('page_title' => lang('registers_report'), 'bc' => $bc);
        $this->page_construct('reports/registers', $this->data, $meta);
    }

    function get_register_logs()
    {

        $user = $this->input->get('user') ? $this->input->get('user') : NULL;
        $start_date = $this->input->get('start_date') ?  $this->ConvertDateTime($this->input->get('start_date')) : NULL;
        $end_date = $this->input->get('end_date') ?  $this->ConvertDateTime($this->input->get('end_date')) : NULL;

        $this->load->library('datatables');
        $this->datatables
        ->select("date as abertura, closed_at as fechamento, CONCAT(" . $this->db->dbprefix('users') . ".first_name, ' ', " . $this->db->dbprefix('users') . ".last_name) as user, cash_in_hand as caixa_inicial, (cash_in_hand + COALESCE(total_cash, 0)) as saldo, COALESCE(total_stripe, 0) as debito, COALESCE(total_CC, 0) as credito, COALESCE(total_cash, 0) as dinheiro, COALESCE(total_pix, 0) as pix, COALESCE(total_reforco, 0) as reforco, COALESCE(total_sangrias, 0) as sangria, note as informacoes", FALSE)
        ->from("registers")
        ->join('users', 'users.id=registers.user_id', 'left');

        if ($user) {
            $this->datatables->where('registers.user_id', $user);
        }
        if ($start_date) {
            $this->datatables->where('date BETWEEN "' . $start_date . '" and "' . $end_date . '"');
        }

        echo $this->datatables->generate();


    }

    function payments()
    {
        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['users'] = $this->reports_model->getAllStaff();
        $this->data['customers'] = $this->reports_model->getAllCustomers();
        $this->data['meiopagamento'] = $this->site->getAllmeiopagamento();
        $bc = array(array('link' => '#', 'page' => lang('reports')), array('link' => '#', 'page' => lang('payments_report')));
        $meta = array('page_title' => lang('payments_report'), 'bc' => $bc);
        $this->page_construct('reports/payments', $this->data, $meta);
    }

    function get_payments()
    {
        $user = $this->input->get('user') ? $this->input->get('user') : NULL;
        $ref = $this->input->get('payment_ref') ? $this->input->get('payment_ref') : NULL;
        $sale_id = $this->input->get('sale_no') ? $this->input->get('sale_no') : NULL;
        $customer = $this->input->get('customer') ? $this->input->get('customer') : NULL;
        $paid_by = $this->input->get('paid_by') ? $this->input->get('paid_by') : NULL;
        $start_date = $this->input->get('start_date') ?  $this->ConvertDateTime($this->input->get('start_date')) : NULL;
        $end_date = $this->input->get('end_date') ?  $this->ConvertDateTime($this->input->get('end_date')) : NULL;

        $this->load->library('datatables');
        $this->datatables
        ->select($this->db->dbprefix('payments') . ".date, " . $this->db->dbprefix('payments') . ".reference as ref, CONCAT('<a href=".site_url('pos/view')."/', ".$this->db->dbprefix('sales').".id,'?from=sales_list class=linkcustomer_1 target=_blank title=Venda alt=Venda>', ".$this->db->dbprefix('sales').".id,'</a>', '<span class=linkcustomer_2 style=display:none;>',  ".$this->db->dbprefix('sales').".id ,'</span>') as sale_no, paid_by, amount")
        ->from('payments')
        ->join('sales', 'payments.sale_id=sales.id', 'left')
        ->group_by('payments.id');

        if ($user) {
            $this->datatables->where('payments.created_by', $user);
        }
        if ($ref) {
            $this->datatables->where('payments.reference', $ref);
        }
        if ($paid_by) {
            $this->datatables->where('payments.paid_by', $paid_by);
        }
        if ($sale_id) {
            $this->datatables->where('sales.id', $sale_id);
        }
        if ($customer) {
            $this->datatables->where('sales.customer_id', $customer);
        }
        if ($customer) {
            $this->datatables->where('sales.customer_id', $customer);
        }
        if ($start_date) {
            $this->datatables->where($this->db->dbprefix('payments').'.date BETWEEN "' . $start_date . '" and "' . $end_date . '"');
        }

        echo $this->datatables->generate();

    }

    function alerts() {
        $data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['page_title'] = lang('stock_alert');
        $bc = array(array('link' => '#', 'page' => lang('stock_alert')));
        $meta = array('page_title' => lang('stock_alert'), 'bc' => $bc);
        $this->page_construct('reports/alerts', $this->data, $meta);

    }

    function get_alerts() {

        $this->load->library('datatables');

        // Verificar se há filtro de data
        $date_filter = $this->input->post('date_filter');

        $this->datatables->select($this->db->dbprefix('products').".id as pid, ".$this->db->dbprefix('products').".image as image, ".$this->db->dbprefix('products').".code as code, ".$this->db->dbprefix('products').".name as pname, type, ".$this->db->dbprefix('categories').".name as cname, quantity, alert_quantity, COALESCE(DATE_FORMAT(MAX(".$this->db->dbprefix('stock_alerts').".alert_date), '%d/%m/%Y %H:%i'), 'N/A') as stock_zero_date, cost, price", FALSE)
        ->join('categories', 'categories.id=products.category_id')
        ->join('stock_alerts', 'stock_alerts.product_id=products.id AND stock_alerts.alert_type="stock_zero"', 'left')
        ->from('products')
        ->where('quantity = 0', NULL, FALSE);

        // Aplicar filtro de data se fornecido
        if ($date_filter && !empty($date_filter)) {
            $this->datatables->where('DATE('.$this->db->dbprefix('stock_alerts').'.alert_date) >=', $date_filter);
        }

        $this->datatables->group_by('products.id');
        $this->datatables->add_column("Actions", "<div class='text-center'><a href='#' class='btn btn-xs btn-primary ap tip' data-id='$1' title='".lang('add_to_purcahse_order')."'><i class='fa fa-plus'></i></a></div>", "pid");
        $this->datatables->unset_column('pid');
        echo $this->datatables->generate();

    }

    /**
     * Get products that need popup alerts
     */
    function get_popup_alerts() {
        try {
            $this->load->model('products_model');

            $alerts = array();

            // Get dates from POST (sent from JavaScript localStorage)
            $stock_start_date = $this->input->post('alerts_date_filter');
            $expiration_start_date = $this->input->post('expiration_alerts_date_filter');

            // Get products with zero stock that should show popup
            $stock_alerts = $this->products_model->getProductsForPopupAlerts($stock_start_date, $expiration_start_date);

            foreach ($stock_alerts as $product) {
                if ($this->products_model->shouldShowPopup($product->id, $product->alert_type)) {
                    $alerts[] = $product;
                }
            }

            // Limit to 3 alerts at a time to avoid overwhelming the user
            $alerts = array_slice($alerts, 0, 3);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => true,
                    'alerts' => $alerts,
                    'count' => count($alerts)
                )));
        } catch (Exception $e) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => false,
                    'error' => $e->getMessage()
                )));
        }
    }

    /**
     * Record user action on popup (X or V button)
     */
    function record_popup_action() {
        $this->load->model('products_model');

        $product_id = $this->input->post('product_id');
        $popup_type = $this->input->post('popup_type');
        $action = $this->input->post('action');

        if (!$product_id || !$popup_type || !$action) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => false,
                    'message' => 'Dados incompletos'
                )));
            return;
        }

        $result = $this->products_model->recordPopupAction($product_id, $popup_type, $action);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(array(
                'success' => $result,
                'message' => $result ? 'Ação registrada com sucesso' : 'Erro ao registrar ação'
            )));
    }

    function expiration_alerts() {
        $data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['page_title'] = 'Alerta de Validade';
        $bc = array(array('link' => '#', 'page' => 'Alerta de Validade'));
        $meta = array('page_title' => 'Alerta de Validade', 'bc' => $bc);
        $this->page_construct('reports/expiration_alerts', $this->data, $meta);
    }

    function get_expiration_alerts() {
        $this->load->library('datatables');

        // Verificar se há filtro de data
        $date_filter = $this->input->post('date_filter');

        // Get current month and year
        $current_month = date('m');
        $current_year = date('Y');

        // Calculate month and year 3 months from now
        $expiry_month = $current_month + 3;
        $expiry_year = $current_year;

        // Adjust if month > 12
        if ($expiry_month > 12) {
            $expiry_month = $expiry_month - 12;
            $expiry_year++;
        }

        // Format as YYYY-MM-DD for the last day of the month
        $start_date = $date_filter && !empty($date_filter) ? $date_filter : date('Y-m-d'); // Use filter date or today
        $end_date = sprintf('%04d-%02d-%02d', $expiry_year, $expiry_month, date('t', strtotime($expiry_year.'-'.$expiry_month.'-01')));
        
        $this->datatables->select($this->db->dbprefix('products').".id as pid, ".$this->db->dbprefix('products').".image as image, ".$this->db->dbprefix('products').".code as code, ".$this->db->dbprefix('products').".name as pname, type, ".$this->db->dbprefix('categories').".name as cname, quantity, validade, cost, price", FALSE)
        ->join('categories', 'categories.id=products.category_id')
        ->from('products')
        ->where('validade !=', NULL)
        ->where('validade !=', '0000-00-00')
        ->where('validade <=', $end_date)
        ->where('validade >=', $start_date)
        ->group_by('products.id');
        
        $this->datatables->add_column("Actions", "<div class='text-center'><a href='#' class='btn btn-xs btn-primary ap tip' data-id='$1' title='".lang('add_to_purcahse_order')."'><i class='fa fa-plus'></i></a></div>", "pid");
        $this->datatables->unset_column('pid');
        echo $this->datatables->generate();
    }





}
