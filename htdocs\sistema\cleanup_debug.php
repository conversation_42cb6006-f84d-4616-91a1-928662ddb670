<?php
// Script para limpar arquivos de debug

echo "<h2>Limpeza de Arquivos de Debug</h2>\n";

$debug_files = array(
    'debug_orcamentos.php',
    'test_orcamento_insert.php',
    'cleanup_debug.php'
);

foreach ($debug_files as $file) {
    if (file_exists($file)) {
        echo "<p>Arquivo encontrado: <strong>$file</strong></p>\n";
        echo "<p><a href='#' onclick=\"if(confirm('Tem certeza que deseja excluir $file?')) { window.location.href='?delete=$file'; }\">🗑️ Excluir $file</a></p>\n";
    }
}

if (isset($_GET['delete'])) {
    $file_to_delete = $_GET['delete'];
    if (in_array($file_to_delete, $debug_files) && file_exists($file_to_delete)) {
        if (unlink($file_to_delete)) {
            echo "<p style='color: green;'>✅ Arquivo $file_to_delete excluído com sucesso!</p>\n";
            echo "<script>setTimeout(function(){ window.location.href = window.location.pathname; }, 2000);</script>\n";
        } else {
            echo "<p style='color: red;'>❌ Erro ao excluir $file_to_delete</p>\n";
        }
    }
}

echo "<h3>Sistema de Orçamentos</h3>\n";
echo "<p>O sistema de orçamentos foi implementado com sucesso!</p>\n";
echo "<ul>\n";
echo "<li><strong>Página de Orçamentos:</strong> <a href='purchases/orcamentos' target='_blank'>purchases/orcamentos</a></li>\n";
echo "<li><strong>Criar Orçamento:</strong> <a href='pos/orcamento' target='_blank'>pos/orcamento</a></li>\n";
echo "</ul>\n";

echo "<h3>Funcionalidades Implementadas:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Criação de orçamentos no POS</li>\n";
echo "<li>✅ Salvamento no banco de dados</li>\n";
echo "<li>✅ Listagem de orçamentos com DataTable</li>\n";
echo "<li>✅ Visualização detalhada de orçamentos</li>\n";
echo "<li>✅ Cálculo automático de saldo a pagar</li>\n";
echo "<li>✅ Formatação de moeda brasileira</li>\n";
echo "<li>✅ Botões de ação (Visualizar, Converter, Excluir)</li>\n";
echo "</ul>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
p { margin: 10px 0; }
</style>
