<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Products_model extends CI_Model
{


    public function __construct() {
        parent::__construct();

    }

    public function getLastProdCode() {
        $this->db->select('code')
        ->limit(1, 0)->order_by("code", "desc");
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return false;
    }

    public function getAllProducts() {
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return false;
    }

    public function products_count($category_id = NULL) {
        if ($category_id) {
            $this->db->where('category_id', $category_id);
            return $this->db->count_all_results("products");
        } else {
            return $this->db->count_all("products");
        }
    }

    public function fetch_products($limit, $start, $category_id = NULL) {
        $this->db->select('name, code, barcode_symbology, price')
        ->limit($limit, $start)->order_by("code", "asc");
        if ($category_id) {
            $this->db->where('category_id', $category_id);
        }
        $q = $this->db->get("products");

        if ($q->num_rows() > 0) {
            foreach ($q->result() as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return false;
    }


	public function getMunicipio($code) {

		$this->db->select('codigo, nome');
		$this->db->from('municipios');
		$this->db->like('uf', $code);
		$q = $this->db->get();

        if ($q->num_rows() > 0) {
            return $q->result();
        }
        return FALSE;
    }


    public function insertImpostos($data) {

        if ($this->db->insert('impostos', $data)) {
            $id = $this->db->insert_id();
            return $id;
        }
        return false;
    }

    public function updateImpostos($id, $data) {

        if ($this->db->update('impostos', $data, array('id' => $id))) {
            
            return true;
        }
        return false;
    }

    public function getImpostos($id = "") {
        
        if($id!=""){
            $q = $this->db->get_where('impostos', array('id' => $id), 1);
            if ($q->num_rows() > 0) {
                return $q->row();
            }
        }else{

            $this->db->order_by('nome');
            $q = $this->db->get('impostos');
            if ($q->num_rows() > 0) {
                foreach (($q->result()) as $row) {
                    $data[] = $row;
                }
                return $data;
            }
        }

        return FALSE;
    }

    public function getImpostosbyNome($nome) {
        
            $q = $this->db->get_where('impostos', array('nome' => $nome), 1);
            if ($q->num_rows() > 0) {
                return TRUE;
            }

        return FALSE;
    }



    public function getProductByCode($code) {
        $q = $this->db->get_where('products', array('code' => $code), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function addProduct($data, $items = array()) {
        // Depuração para verificar os dados recebidos para adicionar o produto
        error_log('Dados recebidos para adicionar produto: ' . print_r($data, true));
        error_log('Campo CEST recebido: ' . (isset($data['cest']) ? $data['cest'] : 'não definido'));
        
        // Verificar se o campo CEST está presente nos dados
        if (isset($data['cest'])) {
            error_log('Valor do campo CEST antes da inserção: ' . $data['cest']);
        } else {
            error_log('Campo CEST não está presente nos dados');
        }
        
        // Ensure validade field is properly formatted
        if (isset($data['validade']) && !empty($data['validade'])) {
            $data['validade'] = trim($data['validade']);
            // Convert mm/aa format to YYYY-MM-DD format
            if (preg_match('/^(\d{2})\/(\d{2})$/', $data['validade'], $matches)) {
                $month = $matches[1];
                $year = '20' . $matches[2];
                // Set to last day of the month
                $data['validade'] = date('Y-m-d', strtotime($year . '-' . $month . '-01 +1 month -1 day'));
            }
        }
        
        if ($this->db->insert('products', $data)) {
            $product_id = $this->db->insert_id();
            
            // Depuração para verificar o produto após a inserção
            $produto_inserido = $this->db->get_where('products', array('id' => $product_id), 1)->row();
            error_log('Produto após inserção: ' . print_r($produto_inserido, true));
            error_log('CEST após inserção: ' . $produto_inserido->cest);
            error_log('Validade após inserção: ' . $produto_inserido->validade);
            
            if(! empty($items)) {
                foreach ($items as $item) {
                    $item['product_id'] = $product_id;
                    $this->db->insert('combo_items', $item);
                }
            }
            return true;
        }
        return false;
    }

    public function add_products($data = array()) {
        if ($this->db->insert_batch('products', $data)) {
            return true;
        }
        return false;
    }

    public function updatePrice($data = array()) {
        if ($this->db->update_batch('products', $data, 'code')) {
            return true;
        }
        return false;
    }

    public function logProductChange($product_id, $field_name, $old_value, $new_value, $user_id, $username) {
        // Só registra se os valores forem diferentes
        if ($old_value != $new_value) {
            $audit_data = array(
                'product_id' => $product_id,
                'field_name' => $field_name,
                'old_value' => $old_value,
                'new_value' => $new_value,
                'user_id' => $user_id,
                'username' => $username
            );
            $this->db->insert('product_audit_log', $audit_data);
        }
    }

    public function getProductAuditLog($product_id) {
        $this->db->select('*');
        $this->db->from('product_audit_log');
        $this->db->where('product_id', $product_id);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit(5); // Limitar a apenas as últimas 5 alterações
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result();
        }
        return FALSE;
    }

    public function updateProduct($id, $data = array(), $items = array(), $photo = NULL) {
        if ($photo) { $data['image'] = $photo; }

        // Get current product data before update to check for stock changes
        $CI =& get_instance();
        $current_product = $CI->site->getProductByID($id);
        $old_quantity = $current_product ? $current_product->quantity : 0;

        // Ensure validade field is properly formatted
        if (isset($data['validade']) && !empty($data['validade'])) {
            $data['validade'] = trim($data['validade']);
            // Convert mm/aa format to YYYY-MM-DD format
            if (preg_match('/^(\d{2})\/(\d{2})$/', $data['validade'], $matches)) {
                $month = $matches[1];
                $year = '20' . $matches[2];
                // Set to last day of the month
                $data['validade'] = date('Y-m-d', strtotime($year . '-' . $month . '-01 +1 month -1 day'));
            }
        }

        if ($this->db->update('products', $data, array('id' => $id))) {
            // Check if quantity changed to zero
            if (isset($data['quantity'])) {
                $new_quantity = floatval($data['quantity']);
                if ($old_quantity > 0 && $new_quantity == 0) {
                    $this->recordStockAlert($id, 'stock_zero', $new_quantity);
                }
            }

            if(! empty($items)) {
                $this->db->delete('combo_items', array('product_id' => $id));
                foreach ($items as $item) {
                    $item['product_id'] = $id;
                    $this->db->insert('combo_items', $item);
                }
            }
            return true;
        }
        return false;
    }

    public function getComboItemsByPID($product_id) {
        $this->db->select($this->db->dbprefix('products') . '.id as id, ' . $this->db->dbprefix('products') . '.code as code, ' . $this->db->dbprefix('combo_items') . '.quantity as qty, ' . $this->db->dbprefix('products') . '.name as name')
        ->join('products', 'products.code=combo_items.item_code', 'left')
        ->group_by('combo_items.id');
        $q = $this->db->get_where('combo_items', array('product_id' => $product_id));
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function deleteProduct($id) {
        if ($this->db->delete('products', array('id' => $id))) {
            return true;
        }
        return FALSE;
    }

    public function getProductNames($term, $limit = 10) {
        $this->db->where("type != 'combo' AND (name LIKE '%" . $term . "%' OR code LIKE '%" . $term . "%' OR  concat(name, ' (', code, ')') LIKE '%" . $term . "%')");
        $this->db->limit($limit);
        $q = $this->db->get('products');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    /**
     * Record stock alert when product reaches zero quantity or expiration
     */
    public function recordStockAlert($product_id, $alert_type, $quantity = 0, $expiration_date = null) {
        $alert_data = array(
            'product_id' => $product_id,
            'alert_type' => $alert_type,
            'quantity_when_alerted' => $quantity,
            'expiration_date' => $expiration_date
        );

        return $this->db->insert($this->db->dbprefix('stock_alerts'), $alert_data);
    }

    /**
     * Get stock alerts for products with zero quantity
     */
    public function getStockZeroAlerts() {
        $this->db->select('sa.*, p.name, p.code, p.image')
                 ->from('stock_alerts sa')
                 ->join('products p', 'p.id = sa.product_id')
                 ->where('sa.alert_type', 'stock_zero')
                 ->order_by('sa.alert_date', 'DESC');

        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Get products that need popup alerts
     */
    public function getProductsForPopupAlerts($stock_start_date = null, $expiration_start_date = null) {
        $stock_alerts = array();

        // Only get stock alerts if start date is configured
        if ($stock_start_date) {
            $this->db->select('p.id, p.name, p.code, p.image, p.quantity, p.validade, "stock_zero" as alert_type, sa.alert_date')
                     ->from('products p')
                     ->join('stock_alerts sa', 'sa.product_id = p.id', 'inner')
                     ->where('p.quantity', 0)
                     ->where('DATE(sa.alert_date) >=', $stock_start_date);

            $stock_alerts = $this->db->get()->result();
        }

        $expiration_alerts = array();

        // Only get expiration alerts if start date is configured
        if ($expiration_start_date) {
            $three_months_from_now = date('Y-m-d', strtotime('+3 months'));
            $this->db->select('p.id, p.name, p.code, p.image, p.quantity, p.validade, "expiration" as alert_type')
                     ->from('products p')
                     ->where('p.validade !=', NULL)
                     ->where('p.validade !=', '0000-00-00')
                     ->where('p.validade <=', $three_months_from_now)
                     ->where('p.validade >=', $expiration_start_date);

            $expiration_alerts = $this->db->get()->result();
        }

        return array_merge($stock_alerts, $expiration_alerts);
    }

    /**
     * Check if popup should be shown for a product
     */
    public function shouldShowPopup($product_id, $popup_type) {
        $this->db->where('product_id', $product_id)
                 ->where('popup_type', $popup_type);

        $control = $this->db->get('popup_controls')->row();

        if (!$control) {
            return true; // First time, show popup
        }

        if ($control->user_action == 'dismissed_v') {
            return false; // Permanently dismissed
        }

        if ($control->user_action == 'dismissed_x' && $control->next_show_time) {
            return strtotime($control->next_show_time) <= time(); // Check if 12 hours passed
        }

        return true;
    }

    /**
     * Record popup action (X or V button clicked)
     */
    public function recordPopupAction($product_id, $popup_type, $action) {
        $next_show_time = null;
        if ($action == 'dismissed_x') {
            $next_show_time = date('Y-m-d H:i:s', strtotime('+12 hours'));
        }

        $data = array(
            'product_id' => $product_id,
            'popup_type' => $popup_type,
            'user_action' => $action,
            'next_show_time' => $next_show_time,
            'last_shown' => date('Y-m-d H:i:s')
        );

        // Use replace to handle both insert and update
        $this->db->query("REPLACE INTO ".$this->db->dbprefix('popup_controls')." (product_id, popup_type, user_action, next_show_time, last_shown, created_at, updated_at)
                         VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                         array($product_id, $popup_type, $action, $next_show_time, date('Y-m-d H:i:s')));

        return $this->db->affected_rows() > 0;
    }

}