<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Purchases extends MY_Controller
{

    function __construct() {
        parent::__construct();

        if (!$this->loggedIn) {
            redirect('login');
        }

        $this->load->library('form_validation');
        $this->load->model('purchases_model');
        $this->allowed_types = 'gif|jpg|png|pdf|doc|docx|xls|xlsx|zip';
    }

    function orcamentos()
    {
        // Verificar se usuário está logado (mais flexível que Admin)
        if (!$this->session->userdata('user_id')) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('login');
        }
        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['page_title'] = 'Orçamentos';
        $bc = array(array('link' => '#', 'page' => 'Orçamentos'));
        $meta = array('page_title' => 'Orçamentos', 'bc' => $bc);
        $this->page_construct('purchases/orcamentos_manual', $this->data, $meta);
    }

    function get_orcamentos()
    {
    	// Verificar se usuário está logado (mais flexível que Admin)
    	if (!$this->session->userdata('user_id')) {
    		$this->session->set_flashdata('error', lang('access_denied'));
    		redirect('login');
    	}

    	try {
    	    // Verificar se a tabela existe primeiro
    	    if (!$this->db->table_exists('tec_purchase_orcamentos')) {
    	        echo json_encode(array(
    	            "draw" => intval($this->input->post('draw')),
    	            "recordsTotal" => 0,
    	            "recordsFiltered" => 0,
    	            "data" => array(),
    	            "error" => "Tabela de orçamentos não encontrada"
    	        ));
    	        return;
    	    }

    	    // Método manual sem datatables library para debug
    	    $draw = intval($this->input->post('draw'));
    	    $start = intval($this->input->post('start'));
    	    $length = intval($this->input->post('length'));

    	    if ($length <= 0) $length = 10;
    	    if ($start < 0) $start = 0;

    	    // Query para contar total
    	    $count_query = "SELECT COUNT(DISTINCT po.id) as total FROM tec_purchase_orcamentos po";
    	    $count_result = $this->db->query($count_query);
    	    $total_records = $count_result->row()->total;

    	    // Query principal
    	    $query = "
    	        SELECT
    	            po.id,
    	            DATE_FORMAT(po.date, '%d/%m/%Y %H:%i') as data_formatada,
    	            COALESCE(po.supplier_name, 'Cliente não informado') as cliente,
    	            COALESCE(po.telefone, '') as telefone,
    	            GROUP_CONCAT(DISTINCT poi.product_name SEPARATOR ', ') as produtos,
    	            COALESCE(po.total_items, 0) as qtd_itens,
    	            COALESCE(po.grand_total, 0) as valor_total,
    	            COALESCE(po.valor_pago, 0) as valor_pago,
    	            (COALESCE(po.grand_total, 0) - COALESCE(po.valor_pago, 0)) as saldo_a_pagar,
    	            COALESCE(po.forma_pagamento, 'Não informado') as forma_pagamento,
    	            COALESCE(po.observacoes, '') as observacoes
    	        FROM tec_purchase_orcamentos po
    	        LEFT JOIN tec_purchase_orcamento_items poi ON po.id = poi.orcamento_id
    	        GROUP BY po.id
    	        ORDER BY po.id DESC
    	        LIMIT " . intval($start) . ", " . intval($length);

    	    $result = $this->db->query($query);
    	    $data = array();

    	    foreach ($result->result_array() as $row) {
    	        $actions = "<div class='text-center'><div class='btn-group'>
    	                      <a href='" . site_url('purchases/view_orcamento/' . $row['id']) . "' title='Visualizar Cupom' class='tip btn btn-primary btn-xs'><i class='fa fa-file-text-o'></i></a>
    	                      <a href='" . site_url('purchases/convert_orcamento/' . $row['id']) . "' title='Adicionar em Compras' class='tip btn btn-success btn-xs'><i class='fa fa-shopping-cart'></i></a>
    	                      <a href='" . site_url('purchases/delete_orcamento/' . $row['id']) . "' onClick=\"return confirm('Tem certeza que deseja excluir este orçamento?')\" title='Excluir' class='tip btn btn-danger btn-xs'><i class='fa fa-trash-o'></i></a></div></div>";

    	        $data[] = array(
    	            $row['data_formatada'],
    	            $row['cliente'],
    	            $row['telefone'],
    	            $row['produtos'] ?: '',
    	            $row['qtd_itens'],
    	            $row['valor_total'],
    	            $row['valor_pago'],
    	            $row['saldo_a_pagar'],
    	            $row['forma_pagamento'],
    	            $row['observacoes'] ?: '',
    	            $actions
    	        );
    	    }

    	    $response = array(
    	        "draw" => $draw,
    	        "recordsTotal" => intval($total_records),
    	        "recordsFiltered" => intval($total_records),
    	        "data" => $data
    	    );

    	    echo json_encode($response);

    	} catch (Exception $e) {
    	    echo json_encode(array(
    	        "draw" => intval($this->input->post('draw')),
    	        "recordsTotal" => 0,
    	        "recordsFiltered" => 0,
    	        "data" => array(),
    	        "error" => "Erro ao carregar dados: " . $e->getMessage()
    	    ));
    	}
    }


    function convert_orcamento($id = NULL)
    {
        // Verificar se usuário está logado (mais flexível que Admin)
        if (!$this->session->userdata('user_id')) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('login');
        }

        if (!$id) {
            $this->session->set_flashdata('error', 'ID do orçamento não fornecido');
            redirect('purchases/orcamentos');
        }

        $this->load->model('purchases_model');
        $orcamento = $this->purchases_model->getOrcamentoByID($id);

        if (!$orcamento) {
            $this->session->set_flashdata('error', 'Orçamento não encontrado');
            redirect('purchases/orcamentos');
        }

        // Redirecionar para adicionar compra com dados do orçamento
        redirect('purchases/add?from_orcamento=' . $id);
    }




	function add_orcamento()
	{
		if (!$this->Admin) {
			$this->session->set_flashdata('error', lang('access_denied'));
			redirect('pos');
		}

		$this->form_validation->set_rules('supplier', lang("supplier"), 'required');
		$this->form_validation->set_rules('reference', lang("reference"), 'required');

		if ($this->form_validation->run() == true) {
			$total = 0;
			$product_tax = 0;
			$order_tax = 0;
			$product_discount = 0;
			$order_discount = 0;
			$percentage = '%';
			$i = sizeof($_POST['product_id']);
			for ($r = 0; $r < $i; $r++) {
				$item_id = $_POST['product_id'][$r];
				$item_qty = $_POST['quantity'][$r];
				$item_cost = $_POST['cost'][$r];
				$item_tax_rate = $_POST['product_tax'][$r];
				$item_discount = $_POST['product_discount'][$r];
				$item_code = $_POST['product_code'][$r];

				if (isset($item_id) && isset($item_code) && isset($item_qty) && isset($item_cost)) {
					$product_details = $this->purchases_model->getProductByCode($item_code);

					$pr_discount = 0;
					if (isset($item_discount)) {
						$discount = $item_discount;
						$dpos = strpos($discount, $percentage);
						if ($dpos !== false) {
							$pds = explode("%", $discount);
							$pr_discount = (($item_cost * $item_qty) * (Float)($pds[0])) / 100;
						} else {
							$pr_discount = $discount;
						}
					}
					$product_discount += $pr_discount;

					$pr_tax = 0;
					$pr_item_tax = 0;
					$item_tax = 0;
					$tax = "";
					if (isset($item_tax_rate) && $item_tax_rate != 0) {
						$pr_tax = $item_tax_rate;
						$tax_details = $this->site->getTaxRateByID($pr_tax);
						if ($tax_details->type == 1 && $tax_details->rate != 0) {
							if ($product_details && $product_details->tax_method == 1) {
								$item_tax = $this->tec->formatDecimal((($item_cost) * $tax_details->rate) / 100);
								$tax = $tax_details->rate . "%";
							} else {
								$item_tax = $this->tec->formatDecimal((($item_cost) * $tax_details->rate) / (100 + $tax_details->rate));
								$tax = $tax_details->rate . "%";
								$item_cost = $item_cost - $item_tax;
							}
						} elseif ($tax_details->type == 2) {
							$item_tax = $this->tec->formatDecimal($tax_details->rate);
							$tax = $tax_details->rate;
						}
						$pr_item_tax = $this->tec->formatDecimal($item_tax * $item_qty);
					}
					$product_tax += $pr_item_tax;
					$subtotal = (($item_cost * $item_qty) + $pr_item_tax) - $pr_discount;
					$total += $subtotal;

					$products[] = array(
						'product_id' => $item_id,
						'product_code' => $item_code,
						'product_name' => $product_details->name,
						'quantity' => $item_qty,
						'cost' => $item_cost,
						'tax' => $tax,
						'tax_rate' => $pr_tax,
						'discount' => $item_discount,
						'item_discount' => $pr_discount,
						'subtotal' => $subtotal,
					);
				}
			}
			if (empty($products)) {
				$this->form_validation->set_rules('product', lang("order_items"), 'required');
			} else {
				krsort($products);
			}

			if ($this->input->post('order_discount')) {
				$order_discount_id = $this->input->post('order_discount');
				$opos = strpos($order_discount_id, $percentage);
				if ($opos !== false) {
					$ods = explode("%", $order_discount_id);
					$order_discount = $this->tec->formatDecimal((($total + $product_tax) * (Float)($ods[0])) / 100);
				} else {
					$order_discount = $this->tec->formatDecimal($order_discount_id);
				}
			} else {
				$order_discount_id = null;
			}
			$total_discount = $this->tec->formatDecimal($order_discount + $product_discount);

			if ($this->input->post('order_tax')) {
				$order_tax_id = $this->input->post('order_tax');
				$opos = strpos($order_tax_id, $percentage);
				if ($opos !== false) {
					$ots = explode("%", $order_tax_id);
					$order_tax = $this->tec->formatDecimal((($total + $product_tax - $order_discount) * (Float)($ots[0])) / 100);
				} else {
					$order_tax = $this->tec->formatDecimal($order_tax_id);
				}
			} else {
				$order_tax_id = null;
				$order_tax = 0;
			}

			$total_tax = $this->tec->formatDecimal($product_tax + $order_tax);
			$grand_total = $this->tec->formatDecimal($this->tec->formatDecimal($total) + $total_tax - $order_discount);
			$data = array(
				'reference' => $this->input->post('reference'),
				'date' => $this->input->post('date'),
				'supplier_id' => $this->input->post('supplier'),
				'supplier_name' => $this->input->post('supplier_name'),
				'note' => $this->input->post('note', TRUE),
				'total' => $this->tec->formatDecimal($total),
				'product_discount' => $this->tec->formatDecimal($product_discount),
				'order_discount_id' => $order_discount_id,
				'order_discount' => $order_discount,
				'total_discount' => $total_discount,
				'product_tax' => $this->tec->formatDecimal($product_tax),
				'order_tax_id' => $order_tax_id,
				'order_tax' => $order_tax,
				'total_tax' => $total_tax,
				'grand_total' => $grand_total,
				'status' => 'pending',
				'created_by' => $this->session->userdata('user_id'),
				'total_items' => $this->input->post('total_items'),
				'total_quantity' => $this->input->post('total_quantity'),
				'forma_pagamento' => $this->input->post('forma_pagamento'),
				'valor_pago' => $this->tec->formatDecimal($this->input->post('valor_pago')),
			);

			if ($this->form_validation->run() == true && $this->purchases_model->addOrcamento($data, $products)) {
				$this->session->set_flashdata('message', 'Orçamento adicionado com sucesso');
				redirect("purchases/orcamentos");
			} else {
				$this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
				$this->data['suppliers'] = $this->site->getAllSuppliers();
				$this->data['tax_rates'] = $this->site->getAllTaxRates();
				$this->data['warehouses'] = $this->site->getAllWarehouses();
				$this->data['ponumber'] = $this->site->getReference('po');
				$this->load->helper('string');
				$value = random_string('alnum', 20);
				$this->session->set_userdata('user_csrf', $value);
				$this->data['csrf'] = $this->session->userdata('user_csrf');
				$bc = array(array('link' => base_url(), 'page' => lang('home')), array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('add_purchase')));
				$meta = array('page_title' => lang('add_purchase'), 'bc' => $bc);
				$this->page_construct('purchases/add_orcamento', $this->data, $meta);
			}
		} else {
			$this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
			$this->data['suppliers'] = $this->site->getAllSuppliers();
			$this->data['tax_rates'] = $this->site->getAllTaxRates();
			$this->data['warehouses'] = $this->site->getAllWarehouses();
			$this->data['ponumber'] = $this->site->getReference('po');
			$this->load->helper('string');
			$value = random_string('alnum', 20);
			$this->session->set_userdata('user_csrf', $value);
			$this->data['csrf'] = $this->session->userdata('user_csrf');
			$bc = array(array('link' => base_url(), 'page' => lang('home')), array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('add_purchase')));
			$meta = array('page_title' => lang('add_purchase'), 'bc' => $bc);
			$this->page_construct('purchases/add_orcamento', $this->data, $meta);
		}
	}

	function view_orcamento($id = NULL)
	{
		// Verificar se usuário está logado (mais flexível que Admin)
		if (!$this->session->userdata('user_id')) {
			$this->session->set_flashdata('error', lang('access_denied'));
			redirect('login');
		}
		if (!$id) {
			$this->session->set_flashdata('error', 'ID do orçamento não fornecido');
			redirect('purchases/orcamentos');
		}

		$this->load->model('purchases_model');
		$orcamento = $this->purchases_model->getOrcamentoByID($id);

		if (!$orcamento) {
			$this->session->set_flashdata('error', 'Orçamento não encontrado');
			redirect('purchases/orcamentos');
		}

		$items = $this->purchases_model->getAllOrcamentoItems($id);

		$this->data['orcamento'] = $orcamento;
		$this->data['items'] = $items;
		$this->data['error'] = $this->session->flashdata('error');

		$bc = array(array('link' => site_url('purchases/orcamentos'), 'page' => 'Orçamentos'), array('link' => '#', 'page' => 'Visualizar Orçamento'));
		$meta = array('page_title' => 'Visualizar Orçamento', 'bc' => $bc);
		$this->page_construct('purchases/view_orcamento_simple', $this->data, $meta);
	}

	function edit_orcamento($id = NULL)
	{
		if (!$this->Admin) {
			$this->session->set_flashdata('error', lang('access_denied'));
			redirect('pos');
		}
		if (!$id) {
			$this->session->set_flashdata('error', lang('no_purchase_selected'));
			redirect('purchases/orcamentos');
		}

		$this->form_validation->set_rules('reference', lang("reference"), 'required');
		$this->form_validation->set_rules('supplier', lang("supplier"), 'required');

		if ($this->form_validation->run() == true) {
			$total = 0;
			$product_tax = 0;
			$order_tax = 0;
			$product_discount = 0;
			$order_discount = 0;
			$percentage = '%';
			$i = sizeof($_POST['product_id']);
			for ($r = 0; $r < $i; $r++) {
				$item_id = $_POST['product_id'][$r];
				$item_qty = $_POST['quantity'][$r];
				$item_cost = $_POST['cost'][$r];
				$item_tax_rate = $_POST['product_tax'][$r];
				$item_discount = $_POST['product_discount'][$r];
				$item_code = $_POST['product_code'][$r];

				if (isset($item_id) && isset($item_code) && isset($item_qty) && isset($item_cost)) {
					$product_details = $this->purchases_model->getProductByCode($item_code);

					$pr_discount = 0;
					if (isset($item_discount)) {
						$discount = $item_discount;
						$dpos = strpos($discount, $percentage);
						if ($dpos !== false) {
							$pds = explode("%", $discount);
							$pr_discount = (($item_cost * $item_qty) * (Float)($pds[0])) / 100;
						} else {
							$pr_discount = $discount;
						}
					}
					$product_discount += $pr_discount;

					$pr_tax = 0;
					$pr_item_tax = 0;
					$item_tax = 0;
					$tax = "";
					if (isset($item_tax_rate) && $item_tax_rate != 0) {
						$pr_tax = $item_tax_rate;
						$tax_details = $this->site->getTaxRateByID($pr_tax);
						if ($tax_details->type == 1 && $tax_details->rate != 0) {
							if ($product_details && $product_details->tax_method == 1) {
								$item_tax = $this->tec->formatDecimal((($item_cost) * $tax_details->rate) / 100);
								$tax = $tax_details->rate . "%";
							} else {
								$item_tax = $this->tec->formatDecimal((($item_cost) * $tax_details->rate) / (100 + $tax_details->rate));
								$tax = $tax_details->rate . "%";
								$item_cost = $item_cost - $item_tax;
							}
						} elseif ($tax_details->type == 2) {
							$item_tax = $this->tec->formatDecimal($tax_details->rate);
							$tax = $tax_details->rate;
						}
						$pr_item_tax = $this->tec->formatDecimal($item_tax * $item_qty);
					}
					$product_tax += $pr_item_tax;
					$subtotal = (($item_cost * $item_qty) + $pr_item_tax) - $pr_discount;
					$total += $subtotal;

					$products[] = array(
						'product_id' => $item_id,
						'product_code' => $item_code,
						'product_name' => $product_details->name,
						'quantity' => $item_qty,
						'cost' => $item_cost,
						'tax' => $tax,
						'tax_rate' => $pr_tax,
						'discount' => $item_discount,
						'item_discount' => $pr_discount,
						'subtotal' => $subtotal,
					);
				}
			}
			if (empty($products)) {
				$this->form_validation->set_rules('product', lang("order_items"), 'required');
			} else {
				krsort($products);
			}

			if ($this->input->post('order_discount')) {
				$order_discount_id = $this->input->post('order_discount');
				$opos = strpos($order_discount_id, $percentage);
				if ($opos !== false) {
					$ods = explode("%", $order_discount_id);
					$order_discount = $this->tec->formatDecimal((($total + $product_tax) * (Float)($ods[0])) / 100);
				} else {
					$order_discount = $this->tec->formatDecimal($order_discount_id);
				}
			} else {
				$order_discount_id = null;
			}
			$total_discount = $this->tec->formatDecimal($order_discount + $product_discount);

			if ($this->input->post('order_tax')) {
				$order_tax_id = $this->input->post('order_tax');
				$opos = strpos($order_tax_id, $percentage);
				if ($opos !== false) {
					$ots = explode("%", $order_tax_id);
					$order_tax = $this->tec->formatDecimal((($total + $product_tax - $order_discount) * (Float)($ots[0])) / 100);
				} else {
					$order_tax = $this->tec->formatDecimal($order_tax_id);
				}
			} else {
				$order_tax_id = null;
				$order_tax = 0;
			}

			$total_tax = $this->tec->formatDecimal($product_tax + $order_tax);
			$grand_total = $this->tec->formatDecimal($this->tec->formatDecimal($total) + $total_tax - $order_discount);
			$data = array(
				'reference' => $this->input->post('reference'),
				'date' => $this->input->post('date'),
				'supplier_id' => $this->input->post('supplier'),
				'supplier_name' => $this->input->post('supplier_name'),
				'note' => $this->input->post('note', TRUE),
				'total' => $this->tec->formatDecimal($total),
				'product_discount' => $this->tec->formatDecimal($product_discount),
				'order_discount_id' => $order_discount_id,
				'order_discount' => $order_discount,
				'total_discount' => $total_discount,
				'product_tax' => $this->tec->formatDecimal($product_tax),
				'order_tax_id' => $order_tax_id,
				'order_tax' => $order_tax,
				'total_tax' => $total_tax,
				'grand_total' => $grand_total,
				'status' => 'pending',
				'updated_by' => $this->session->userdata('user_id'),
				'total_items' => $this->input->post('total_items'),
				'total_quantity' => $this->input->post('total_quantity'),
			);

			if ($this->form_validation->run() == true && $this->purchases_model->updateOrcamento($id, $data, $products)) {
				$this->session->set_flashdata('message', 'Orçamento atualizado com sucesso');
				redirect("purchases/orcamentos");
			} else {
				$this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
				$this->data['purchase'] = $this->purchases_model->getOrcamentoByID($id);
				$this->data['items'] = $this->purchases_model->getAllOrcamentoItems($id);
				$this->data['suppliers'] = $this->site->getAllSuppliers();
				$this->data['tax_rates'] = $this->site->getAllTaxRates();
				$this->data['warehouses'] = $this->site->getAllWarehouses();
				$this->load->helper('string');
				$value = random_string('alnum', 20);
				$this->session->set_userdata('user_csrf', $value);
				$this->data['csrf'] = $this->session->userdata('user_csrf');
				$bc = array(array('link' => base_url(), 'page' => lang('home')), array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('edit_purchase')));
				$meta = array('page_title' => lang('edit_purchase'), 'bc' => $bc);
				$this->page_construct('purchases/edit_orcamento', $this->data, $meta);
			}
		} else {
			$this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
			$this->data['purchase'] = $this->purchases_model->getOrcamentoByID($id);
			$this->data['items'] = $this->purchases_model->getAllOrcamentoItems($id);
			$this->data['suppliers'] = $this->site->getAllSuppliers();
			$this->data['tax_rates'] = $this->site->getAllTaxRates();
			$this->data['warehouses'] = $this->site->getAllWarehouses();
			$this->load->helper('string');
			$value = random_string('alnum', 20);
			$this->session->set_userdata('user_csrf', $value);
			$this->data['csrf'] = $this->session->userdata('user_csrf');
			$bc = array(array('link' => base_url(), 'page' => lang('home')), array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('edit_purchase')));
			$meta = array('page_title' => lang('edit_purchase'), 'bc' => $bc);
			$this->page_construct('purchases/edit_orcamento', $this->data, $meta);
		}
	}

	function delete_orcamento($id = NULL)
	{
		// Verificar se usuário está logado (mais flexível que Admin)
		if (!$this->session->userdata('user_id')) {
			$this->session->set_flashdata('error', lang('access_denied'));
			redirect('login');
		}
		if (!$id) {
			$this->session->set_flashdata('error', lang('no_purchase_selected'));
			redirect('purchases/orcamentos');
		}

		if ($this->purchases_model->deleteOrcamento($id)) {
			$this->session->set_flashdata('message', 'Orçamento excluído com sucesso');
			redirect('purchases/orcamentos');
		}
	}



    function index() {
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
        $this->data['page_title'] = lang('purchases');
        $bc = array(array('link' => '#', 'page' => lang('purchases')));
        $meta = array('page_title' => lang('purchases'), 'bc' => $bc);
        $this->page_construct('purchases/index', $this->data, $meta);

    }

    function get_purchases() {
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        $this->load->library('datatables');
        $this->datatables->select("id, date, reference, total, note, attachment");
        $this->datatables->from('purchases');
        $this->datatables->add_column("Actions", "<div class='text-center'><div class='btn-group'><a data-fancybox href='" .  site_url('purchases/view/$1')."', 'pos_popup' title='".lang('print_barcodes')."' class='tip btn btn-primary btn-xs'><i class='fa fa-file-text-o'></i></a> <a href='" . site_url('purchases/edit/$1') . "' title='" . lang("edit_purchase") . "' class='tip btn btn-warning btn-xs'><i class='fa fa-edit'></i></a> <a href='" . site_url('purchases/delete/$1') . "' onClick=\"return confirm('" . lang('alert_x_purchase') . "')\" title='" . lang("delete_purchase") . "' class='tip btn btn-danger btn-xs'><i class='fa fa-trash-o'></i></a></div></div>", "id");

        $this->datatables->unset_column('id');
        echo $this->datatables->generate();

    }

    function view($id = NULL) {
        if (!$this->Admin) {
            $this->session->set_flashdata('error', $this->lang->line('access_denied'));
            redirect('pos');
        }

        $this->load->helper('download');

        // Buscar os itens da compra
        $this->db->select('products.code, products.name')
            ->from('purchase_items')
            ->join('products', 'products.id = purchase_items.product_id')
            ->where('purchase_id', $id);

        $query = $this->db->get();
        $items = $query->result();

        // Criar conteúdo do arquivo
        $content = "";
        foreach ($items as $item) {
            $content .= $item->code . " - " . $item->name . "\n";
        }

        force_download('produtos_da_compra_' . $id . '.txt', $content);
    }

    function add() {
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        $this->form_validation->set_rules('date', lang('date'), 'required');

        if ($this->form_validation->run() == true) {
            $total = 0;
            $quantity = "quantity";
            $product_id = "product_id";
            $unit_cost = "cost";
            $i = isset($_POST['product_id']) ? sizeof($_POST['product_id']) : 0;
            for ($r = 0; $r < $i; $r++) {
                $item_id = $_POST['product_id'][$r];
                $item_qty = $this->tec->formatDolar($_POST['quantity'][$r], 3);
                $item_cost = $this->tec->formatDolar($_POST['cost'][$r]);
                if( $item_id && $item_qty && $unit_cost ) {

                    if(!$this->site->getProductByID($item_id)) {
                        $this->session->set_flashdata('error', $this->lang->line("product_not_found")." ( ".$item_id." ).");
                        redirect('purchases/add');
                    }

                    $products[] = array(
                        'product_id' => $item_id,
                        'cost' => $item_cost,
                        'quantity' => $item_qty,
                        'subtotal' => ($item_cost*$item_qty)
                        );

                    $total += ($item_cost * $item_qty);

                }
            }

            if (!isset($products) || empty($products)) {
                $this->form_validation->set_rules('product', lang("order_items"), 'required');
            } else {
                krsort($products);
            }

            $data = array(
                'date' => $this->tec->FormatarDataTimeBRParaDB($this->input->post('date')),
                'reference' => $this->input->post('reference'),
                'note' => $this->input->post('note', TRUE),
                'total' => $total
            );

            if ($_FILES['userfile']['size'] > 0) {

                $this->load->library('upload');
                $config['upload_path'] = 'uploads/';
                $config['allowed_types'] = $this->allowed_types;
                $config['max_size'] = '200000';
                $config['overwrite'] = FALSE;
                $config['encrypt_name'] = TRUE;
                $this->upload->initialize($config);

                if (!$this->upload->do_upload()) {
                    $error = $this->upload->display_errors();
                    $this->upload->set_flashdata('error', $error);
                    redirect("purchases/add");
                }

                $data['attachment'] = $this->upload->file_name;

            }
            //$this->tec->print_arrays($data, $products);
        }

        if ($this->form_validation->run() == true && $this->purchases_model->addPurchase($data, $products)) {

            $this->session->set_userdata('remove_spo', 1);
            $this->session->set_flashdata('message', lang('purchase_added'));
            redirect("purchases");

        } else {

            $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
            $this->data['suppliers'] = $this->site->getAllSuppliers();
            $this->data['page_title'] = lang('add_purchase');
            $bc = array(array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('add_purchase')));
            $meta = array('page_title' => lang('add_purchase'), 'bc' => $bc);
            $this->page_construct('purchases/add', $this->data, $meta);

        }
    }

    function edit($id = NULL) {
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        if ($this->input->get('id')) {
            $id = $this->input->get('id');
        }

        $this->form_validation->set_rules('date', lang('date'), 'required');

        if ($this->form_validation->run() == true) {
            $total = 0;
            $quantity = "quantity";
            $product_id = "product_id";
            $unit_cost = "cost";
            $i = isset($_POST['product_id']) ? sizeof($_POST['product_id']) : 0;
            for ($r = 0; $r < $i; $r++) {
                $item_id = $_POST['product_id'][$r];
                $item_qty = $this->tec->formatDolar($_POST['quantity'][$r], 3);
                $item_cost = $this->tec->formatDolar($_POST['cost'][$r]);
                if( $item_id && $item_qty && $unit_cost ) {

                    if(!$this->site->getProductByID($item_id)) {
                        $this->session->set_flashdata('error', $this->lang->line("product_not_found")." ( ".$item_id." ).");
                        redirect('purchases/edit/'.$id);
                    }

                    $products[] = array(
                        'product_id' => $item_id,
                        'cost' => $item_cost,
                        'quantity' => $item_qty,
                        'subtotal' => ($item_cost*$item_qty)
                        );

                    $total += ($item_cost * $item_qty);

                }
            }

            if (!isset($products) || empty($products)) {
                $this->form_validation->set_rules('product', lang("order_items"), 'required');
            } else {
                krsort($products);
            }

            $data = array(
                        'date' => $this->tec->FormatarDataTimeBRParaDB($this->input->post('date')),
                        'reference' => $this->input->post('reference'),
                        'note' => $this->input->post('note', TRUE),
                        'total' => $total
                    );

            if ($_FILES['userfile']['size'] > 0) {

                $this->load->library('upload');
                $config['upload_path'] = 'uploads/';
                $config['allowed_types'] = $this->allowed_types;
                $config['max_size'] = '200000';
                $config['overwrite'] = FALSE;
                $config['encrypt_name'] = TRUE;
                $this->upload->initialize($config);

                if (!$this->upload->do_upload()) {
                    $error = $this->upload->display_errors();
                    $this->upload->set_flashdata('error', $error);
                    redirect("purchases/add");
                }

                $data['attachment'] = $this->upload->file_name;

            }
            // $this->tec->print_arrays($data, $products);
        }

        if ($this->form_validation->run() == true && $this->purchases_model->updatePurchase($id, $data, $products)) {

            $this->session->set_userdata('remove_spo', 1);
            $this->session->set_flashdata('message', lang('purchase_updated'));
            redirect("purchases");

        } else {

            $this->data['purchase'] = $this->purchases_model->getPurchaseByID($id);
            $inv_items = $this->purchases_model->getAllPurchaseItems($id);
            $c = rand(100000, 9999999);
            foreach ($inv_items as $item) {
                $row = $this->site->getProductByID($item->product_id);
                $row->qty = $item->quantity;
                $row->cost = $item->cost;
                $ri = $this->Settings->item_addition ? $row->id : $c;
                $pr[$ri] = array('id' => $ri, 'item_id' => $row->id, 'label' => $row->name . " (" . $row->code . ")", 'row' => $row);
                $c++;
            }

            $this->data['items'] = json_encode($pr);
            $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
            $this->data['suppliers'] = $this->site->getAllSuppliers();
            $this->data['page_title'] = lang('edit_purchase');
            $bc = array(array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('edit_purchase')));
            $meta = array('page_title' => lang('edit_purchase'), 'bc' => $bc);
            $this->page_construct('purchases/edit', $this->data, $meta);

        }
    }

    function delete($id = NULL) {
        if(DEMO) {
            $this->session->set_flashdata('error', lang('disabled_in_demo'));
            redirect(isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'welcome');
        }
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        if ($this->input->get('id')) {
            $id = $this->input->get('id');
        }

        if ($this->purchases_model->deletePurchase($id)) {
            $this->session->set_flashdata('message', lang("purchase_deleted"));
            redirect('purchases');
        }
    }


     /* ----------------------------------------------------------------- */

     function expenses($id = NULL)
    {

        $this->data['error'] = (validation_errors()) ? validation_errors() : $this->session->flashdata('error');
        $this->data['page_title'] = lang('expenses');
        $bc = array(array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => '#', 'page' => lang('expenses')));
        $meta = array('page_title' => lang('expenses'), 'bc' => $bc);
        $this->page_construct('purchases/expenses', $this->data, $meta);

    }

    function get_expenses($user_id = NULL)
    {

        /*
        $detail_link = anchor('purchases/expense_note/$1', '<i class="fa fa-file-text-o"></i> ' . lang('expense_note'), 'data-toggle="modal" data-target="#myModal2"');
        $edit_link = anchor('purchases/edit_expense/$1', '<i class="fa fa-edit"></i> ' . lang('edit_expense'), 'data-toggle="modal" data-target="#myModal"');
        $delete_link = "<a href='#' class='po' title='<b>" . $this->lang->line("delete_expense") . "</b>' data-content=\"<p>"
            . lang('r_u_sure') . "</p><a class='btn btn-danger po-delete' href='" . site_url('purchases/delete_expense/$1') . "'>"
            . lang('i_m_sure') . "</a> <button class='btn po-close'>" . lang('no') . "</button>\"  rel='popover'><i class=\"fa fa-trash-o\"></i> "
            . lang('delete_expense') . "</a>";
        $action = '<div class="text-center" style="width:100px;"><div class="btn-group text-left">'
            . '<button type="button" class="btn btn-default btn-xs btn-primary dropdown-toggle" data-toggle="dropdown">'
            . lang('actions') . ' <span class="caret"></span></button>
        <ul class="dropdown-menu pull-right" role="menu">
            <li>' . $detail_link . '</li>
            <li>' . $edit_link . '</li>
            <li>' . $delete_link . '</li>
        </ul>
    </div></div>';*/


        $this->load->library('datatables');
        $this->datatables
            ->select("status, ". $this->db->dbprefix('expenses') . ".id as id, date, reference, amount, note, CONCAT(" . $this->db->dbprefix('users') . ".first_name, ' ', " . $this->db->dbprefix('users') . ".last_name) as user, attachment", FALSE)
            ->from('expenses')
            ->join('users', 'users.id=expenses.created_by', 'left')
            ->group_by('expenses.id');


        $start_date = $this->input->get('start_date') ? $this->input->get('start_date') : NULL;
        $end_date = $this->input->get('end_date') ? $this->input->get('end_date') : NULL;
        $status = $this->input->get('status')!="" ? $this->input->get('status') : NULL;
        // filtros
        if($start_date!="") { $this->datatables->where('date >=', $this->tec->FormatarDataTimeBRParaDB($start_date));  } // formatar data
        if($end_date!="") { $this->datatables->where('date <=', $this->tec->FormatarDataTimeBRParaDB($end_date)); } // formatar data

        if($status!="") { $this->datatables->where('status =', $status); }

        if ( ! $this->Admin) {
            $this->datatables->where('created_by', $this->session->userdata('user_id'));
        }

        $this->datatables->add_column("Actions", "<div class='text-center'><div class='btn-group'  style='width:70px;'><a data-fancybox href='" . site_url('purchases/expense_note/$1')."' title='".lang('expense_note')."' class='tip btn btn-primary btn-xs'><i class='fa fa-file-text-o'></i></a> <a href='" . site_url('purchases/edit_expense/$1') . "' title='" . lang("edit_expense") . "' class='tip btn btn-warning btn-xs'><i class='fa fa-edit'></i></a> <a href='" . site_url('purchases/delete_expense/$1') . "' onClick=\"return confirm('Deseja realmente excluir?')\" title='" . lang("delete_expense") . "' class='tip btn btn-danger btn-xs'><i class='fa fa-trash-o'></i></a></div></div>", "id");
        $this->datatables->unset_column('id');
        echo $this->datatables->generate();
    }

    function expense_note($id = NULL)
    {
        $expense = $this->purchases_model->getExpenseByID($id);
        if ( ! $this->Admin) {
            if($expense->created_by != $this->session->userdata('user_id')) {
                $this->session->set_flashdata('error', lang('access_denied'));
                redirect(isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'pos');
            }
        }

        $this->data['user'] = $this->site->getUser($expense->created_by);
        $this->data['expense'] = $expense;
        $this->data['page_title'] = $this->lang->line("expense_note");
        $this->load->view($this->theme . 'purchases/expense_note', $this->data);

    }

    function add_expense()
    {
        $this->load->helper('security');

        $this->form_validation->set_rules('amount', lang("amount"), 'required');
        $this->form_validation->set_rules('userfile', lang("attachment"), 'xss_clean');
        if ($this->form_validation->run() == true) {
            //if ($this->Admin) {
            if($this->input->post('date')!=""){
                $date = $this->tec->FormatarDataTimeBRParaDB(trim($this->input->post('date'). " 00:01"));
            } else {
                $date = date('Y-m-d H:i:s');
            }
            $data = array(
                'date' => $date,
                'reference' => $this->input->post('reference') ? $this->input->post('reference') : "Despesa",
                'amount' => str_replace(",", ".", str_replace(".", "", $this->input->post('amount'))),
                'created_by' => $this->session->userdata('user_id'),
                'note' => $this->input->post('note', TRUE),
                'status' => $this->input->post('status'),
                'vencimento' => $this->input->post('vencimento'),
            );

            if ($_FILES['userfile']['size'] > 0) {
                $this->load->library('upload');
                $config['upload_path'] = 'uploads/';
                $config['allowed_types'] = $this->allowed_types;
                $config['max_size'] = '200000';
                $config['overwrite'] = FALSE;
                $config['encrypt_name'] = TRUE;
                $this->upload->initialize($config);
                if (!$this->upload->do_upload()) {
                    $error = $this->upload->display_errors();
                    $this->session->set_flashdata('error', $error);
                    redirect($_SERVER["HTTP_REFERER"]);
                }
                $photo = $this->upload->file_name;
                $data['attachment'] = $photo;
            }

            //$this->tec->print_arrays($data);

        } elseif ($this->input->post('add_expense')) {
            $this->session->set_flashdata('error', validation_errors());
            redirect($_SERVER["HTTP_REFERER"]);
        }

        if ($this->form_validation->run() == true && $this->purchases_model->addExpense($data)) {

            $this->session->set_flashdata('message', lang("expense_added"));
            redirect('purchases/expenses');

        } else {

            $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
            $this->data['page_title'] = lang('add_expense');
            $bc = array(array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => site_url('purchases/expenses'), 'page' => lang('expenses')), array('link' => '#', 'page' => lang('add_expense')));
            $meta = array('page_title' => lang('add_expense'), 'bc' => $bc);
            $this->page_construct('purchases/add_expense', $this->data, $meta);

        }
    }

    function edit_expense($id = NULL)
    {
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        $this->load->helper('security');
        if ($this->input->get('id')) {
            $id = $this->input->get('id');
        }

        $this->form_validation->set_rules('reference', lang("reference"), 'required');
        $this->form_validation->set_rules('amount', lang("amount"), 'required');
        $this->form_validation->set_rules('userfile', lang("attachment"), 'xss_clean');
        if ($this->form_validation->run() == true) {
            //if ($this->Admin) {
            if($this->input->post('date')!=""){
                $date = $this->tec->FormatarDataTimeBRParaDB(trim($this->input->post('date'). " 00:01"));
            } else {
                $date = date('Y-m-d H:i:s');
            }
            $data = array(
                'date' => $date,
                'reference' => $this->input->post('reference'),
                'amount' => str_replace(",", ".", str_replace(".", "", $this->input->post('amount'))),
                'note' => $this->input->post('note', TRUE),
                'status' => $this->input->post('status'),
                //'vencimento' => $this->input->post('vencimento'),
            );
            if ($_FILES['userfile']['size'] > 0) {
                $this->load->library('upload');
                $config['upload_path'] = 'uploads/';
                $config['allowed_types'] = $this->allowed_types;
                $config['max_size'] = '200000';
                $config['overwrite'] = FALSE;
                $config['encrypt_name'] = TRUE;
                $this->upload->initialize($config);
                if (!$this->upload->do_upload()) {
                    $error = $this->upload->display_errors();
                    $this->session->set_flashdata('error', $error);
                    redirect($_SERVER["HTTP_REFERER"]);
                }
                $photo = $this->upload->file_name;
                $data['attachment'] = $photo;
            }

            //$this->tec->print_arrays($data);

        } elseif ($this->input->post('edit_expense')) {
            $this->session->set_flashdata('error', validation_errors());
            redirect($_SERVER["HTTP_REFERER"]);
        }


        if ($this->form_validation->run() == true && $this->purchases_model->updateExpense($id, $data)) {
            $this->session->set_flashdata('message', lang("expense_updated"));
            redirect("purchases/expenses");
        } else {

            $this->data['error'] = (validation_errors() ? validation_errors() : $this->session->flashdata('error'));
            $this->data['expense'] = $this->purchases_model->getExpenseByID($id);
            $this->data['page_title'] = lang('edit_expense');
            $bc = array(array('link' => site_url('purchases'), 'page' => lang('purchases')), array('link' => site_url('purchases/expenses'), 'page' => lang('expenses')), array('link' => '#', 'page' => lang('edit_expense')));
            $meta = array('page_title' => lang('edit_expense'), 'bc' => $bc);
            $this->page_construct('purchases/edit_expense', $this->data, $meta);

        }
    }

    function delete_expense($id = NULL)
    {
        if(DEMO) {
            $this->session->set_flashdata('error', lang('disabled_in_demo'));
            redirect(isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : 'welcome');
        }
        if ( ! $this->Admin) {
            $this->session->set_flashdata('error', lang('access_denied'));
            redirect('pos');
        }
        if ($this->input->get('id')) {
            $id = $this->input->get('id');
        }

        $expense = $this->purchases_model->getExpenseByID($id);
        if ($this->purchases_model->deleteExpense($id)) {
            if ($expense->attachment) {
                unlink($this->upload_path . $expense->attachment);
            }
            $this->session->set_flashdata('message', lang("expense_deleted"));
            redirect("purchases/expenses");
        }
    }
}