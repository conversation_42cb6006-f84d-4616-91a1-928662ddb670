-- Execute este script no php<PERSON>yAdmin ou MySQL para criar as tabelas de alertas de estoque
-- Selecione o banco de dados sis_nfe_pdv antes de executar

-- --------------------------------------------------------

--
-- Estrutura para tabela `tec_stock_alerts`
-- <PERSON><PERSON> quando produtos atingem estoque zero ou estão próximos ao vencimento
--

CREATE TABLE IF NOT EXISTS `tec_stock_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `alert_type` enum('stock_zero','expiration') NOT NULL,
  `alert_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `quantity_when_alerted` decimal(15,3) DEFAULT 0.000,
  `expiration_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMAR<PERSON>EY (`id`),
  <PERSON>EY `product_id` (`product_id`),
  KEY `alert_type` (`alert_type`),
  KEY `alert_date` (`alert_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tec_popup_controls`
-- Controla a exibição de popups de alertas para cada usuário
--

CREATE TABLE IF NOT EXISTS `tec_popup_controls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `popup_type` enum('stock_zero','expiration') NOT NULL,
  `last_shown` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_action` enum('dismissed_x','dismissed_v','pending') DEFAULT 'pending',
  `next_show_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `popup_type` (`popup_type`),
  KEY `next_show_time` (`next_show_time`),
  UNIQUE KEY `unique_product_popup` (`product_id`, `popup_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
