# Instruções para Alertas de Estoque e Popups

## Funcionalidades Implementadas

### 1. Coluna Data (e hora) nos Alertas de Estoque
- Adicionada nova coluna na página de alertas de estoque (`/sistema/reports/alerts`)
- Mostra quando o produto atingiu estoque zero
- Formato: DD/MM/AAAA HH:MM

### 2. Sistema de Popups de Alertas
- Popups automáticos para produtos com estoque zero
- Popups automáticos para produtos próximos ao vencimento (3 meses)
- Controles X (vermelho) e V (verde)
- Sistema de timing para reexibição

## Instalação

### 1. Executar Script SQL
Execute o arquivo `htdocs/sistema/install/stock_alerts_tables.sql` no phpMyAdmin:

```sql
-- Selecione o banco de dados sis_nfe_pdv antes de executar
-- Execute todo o conteúdo do arquivo stock_alerts_tables.sql
```

### 2. Verificar Arquivos Criados/Modificados

#### Novos Arquivos:
- `htdocs/sistema/install/stock_alerts_tables.sql`
- `htdocs/sistema/themes/default/assets/dist/css/popup-alerts.css`
- `htdocs/sistema/themes/default/assets/dist/js/popup-alerts.js`
- `htdocs/sistema/INSTRUCOES_ALERTAS_POPUP.md`

#### Arquivos Modificados:
- `htdocs/sistema/install/sis_nfe_pdv.sql` (estrutura das tabelas)
- `htdocs/sistema/app/models/Products_model.php` (métodos de rastreamento)
- `htdocs/sistema/app/controllers/Reports.php` (novos endpoints)
- `htdocs/sistema/themes/default/views/reports/alerts.php` (nova coluna)
- `htdocs/sistema/themes/default/views/header.php` (CSS)
- `htdocs/sistema/themes/default/views/footer.php` (JS e variáveis)

## Como Testar

### 1. Testar Coluna de Data/Hora
1. Acesse `/sistema/reports/alerts`
2. Verifique se há uma nova coluna "Data (e hora)"
3. Para produtos com estoque zero, deve mostrar a data/hora
4. Para produtos sem histórico, deve mostrar "N/A"

### 2. Testar Popups de Estoque Zero
1. Crie ou edite um produto para ter quantidade = 0
2. Acesse qualquer página do sistema
3. Deve aparecer um popup vermelho com:
   - Ícone de alerta
   - Imagem do produto
   - Nome e código do produto
   - Botões X (vermelho) e V (verde)

### 3. Testar Popups de Validade
1. Crie ou edite um produto com validade próxima (até 3 meses)
2. Acesse qualquer página do sistema
3. Deve aparecer um popup laranja com:
   - Ícone de calendário
   - Imagem do produto
   - Nome, código e data de validade
   - Botões X (vermelho) e V (verde)

### 4. Testar Controles dos Popups

#### Botão X (Vermelho):
- Clique no botão "Lembrar em 12h"
- O popup deve fechar
- O mesmo popup deve reaparecer após 12 horas

#### Botão V (Verde):
- Clique no botão "Não mostrar mais"
- O popup deve fechar
- O mesmo popup NÃO deve aparecer novamente para aquele produto

### 5. Testar Múltiplos Popups
- Tenha vários produtos com estoque zero ou próximos ao vencimento
- Os popups devem aparecer um por vez
- Máximo de 3 alertas por verificação

## Configurações

### Frequência de Verificação
- Os popups são verificados a cada 5 minutos
- Pode ser alterado no arquivo `popup-alerts.js` (linha 15)

### Limite de Alertas Simultâneos
- Máximo de 3 alertas por verificação
- Pode ser alterado no controlador `Reports.php` (método `get_popup_alerts`)

### Tempo de Reexibição
- 12 horas após clicar no X
- Pode ser alterado no modelo `Products_model.php` (método `recordPopupAction`)

## Troubleshooting

### Popups não aparecem:
1. Verifique se as tabelas foram criadas corretamente
2. Verifique se há produtos com estoque zero ou próximos ao vencimento
3. Verifique o console do navegador para erros JavaScript
4. Verifique se os arquivos CSS e JS estão sendo carregados

### Coluna de data não aparece:
1. Verifique se a tabela `tec_stock_alerts` foi criada
2. Limpe o cache do navegador
3. Verifique se não há erros no console

### Ações dos botões não funcionam:
1. Verifique se a tabela `tec_popup_controls` foi criada
2. Verifique se os tokens CSRF estão sendo passados corretamente
3. Verifique os logs do servidor para erros PHP

## Estrutura das Tabelas

### tec_stock_alerts
- Registra quando produtos atingem estoque zero ou estão próximos ao vencimento
- Campos: id, product_id, alert_type, alert_date, quantity_when_alerted, expiration_date

### tec_popup_controls
- Controla a exibição de popups para cada produto
- Campos: id, product_id, popup_type, last_shown, user_action, next_show_time
