<?php
$v = "?v=1";
?>
<script>
$(document).ready(function () {

    function eventFired(t){
       var v_total = 0;
       var v_pago = 0;
       var v_saldo = 0;

       $('#OrcData tbody tr').each(function() {
           // Valor Total (coluna 6)
           v = $(this).find("td:nth-child(6)").text().replace(".", "").replace(",", ".");
           v_total += parseFloat((v=="" || v==0 || v==null)? 0: v);

           // Valor Pago (coluna 7)
           v = $(this).find("td:nth-child(7)").text().replace(".", "").replace(",", ".");
           v_pago += parseFloat((v=="" || v==0 || v==null)? 0: v);

           // Saldo (coluna 8)
           v = $(this).find("td:nth-child(8)").text().replace(".", "").replace(",", ".");
           v_saldo += parseFloat((v=="" || v==0|| v==null)? 0: v);
       });

       $('#OrcData tbody').append('<tr style="background-color: #f5f5f5; font-weight: bold;"><td></td><td></td><td></td><td></td><td></td><td><div style="text-align:right">'+numberToReal(v_total)+'</div></td><td><div style="text-align:right">'+numberToReal(v_pago)+'</div></td><td><div style="text-align:right">'+numberToReal(v_saldo)+'</div></td><td></td><td></td><td></td></tr>');
    }
    
    function numberToReal(numero = "", decimal = 2) {
        if(numero=="" || numero == null) return "0,00";
        return new Intl.NumberFormat('pt-BR', { 
            style: 'decimal', 
            minimumFractionDigits: decimal, 
            maximumFractionDigits: decimal 
        }).format(numero);
    }

    // Debug: Testar endpoint antes de inicializar DataTable
    console.log('Testando endpoint antes de inicializar DataTable...');

    $.ajax({
        url: '<?= site_url('purchases/get_orcamentos' . $v) ?>',
        type: 'POST',
        data: {
            'draw': 1,
            'start': 0,
            'length': 10,
            '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
        },
        dataType: 'json',
        success: function(response) {
            console.log('✅ Endpoint funcionando:', response);
            initializeDataTable();
        },
        error: function(xhr, status, error) {
            console.error('❌ Endpoint com erro:', error);
            console.error('Status:', status);
            console.error('Resposta:', xhr.responseText);
            console.error('HTTP Status:', xhr.status);

            // Mostrar erro na tabela
            $('#OrcData_processing').hide();
            $('#OrcData tbody').html('<tr><td colspan="11" class="text-center text-danger"><strong>Erro ao carregar dados:</strong><br>' + error + '<br><small>' + xhr.responseText.substring(0, 200) + '</small></td></tr>');
        }
    });

    function initializeDataTable() {
        console.log('Inicializando DataTable...');

        $('#OrcData').dataTable({
        "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, 'Todos']],
        "aaSorting": [[ 0, "desc" ]],
        "iDisplayLength": 50,
        'bProcessing': true,
        'bServerSide': true,
        'sAjaxSource': '<?= site_url('purchases/get_orcamentos' . $v) ?>',
        "aoColumns": [
            null, null, null, null, null, null, null, null, null, null, null
        ],
        'fnServerData': function (sSource, aoData, fnCallback) {
            aoData.push({
                "name": "<?= $this->security->get_csrf_token_name() ?>",
                "value": "<?= $this->security->get_csrf_hash() ?>"
            });
            $.ajax({
                'dataType': 'json',
                'type': 'POST',
                'url': sSource,
                'data': aoData,
                'success': function(response) {
                    console.log('Resposta recebida:', response);
                    console.log('Tipo da resposta:', typeof response);
                    console.log('Keys da resposta:', Object.keys(response));

                    // Debug detalhado da estrutura
                    if (response.data) {
                        console.log('response.data existe:', Array.isArray(response.data));
                        console.log('response.data.length:', response.data.length);
                        if (response.data.length > 0) {
                            console.log('Primeiro item:', response.data[0]);
                            console.log('Tipo do primeiro item:', typeof response.data[0]);
                            console.log('É array?', Array.isArray(response.data[0]));
                            if (Array.isArray(response.data[0])) {
                                console.log('Colunas do primeiro item:', response.data[0].length);
                            }
                        }
                    }

                    // Verificar se a resposta tem a estrutura esperada
                    if (response && typeof response === 'object') {
                        if (response.data && Array.isArray(response.data)) {
                            console.log('✅ Dados válidos:', response.data.length + ' registros');

                            // Verificar se cada item do data é um array
                            let allItemsAreArrays = response.data.every(item => Array.isArray(item));
                            console.log('Todos os itens são arrays?', allItemsAreArrays);

                            if (!allItemsAreArrays) {
                                console.error('❌ Nem todos os itens são arrays!');
                                // Converter objetos para arrays se necessário
                                response.data = response.data.map(item => {
                                    if (Array.isArray(item)) return item;
                                    return Object.values(item);
                                });
                                console.log('Dados convertidos para arrays');
                            }

                            // Garantir que a resposta tem a estrutura exata esperada pelo DataTables
                            var safeResponse = {
                                "draw": parseInt(response.draw) || 1,
                                "recordsTotal": parseInt(response.recordsTotal) || 0,
                                "recordsFiltered": parseInt(response.recordsFiltered) || 0,
                                "data": response.data || []
                            };

                            console.log('Resposta final para DataTables:', safeResponse);
                            fnCallback(safeResponse);
                        } else {
                            console.error('❌ Estrutura de dados inválida:', response);
                            fnCallback({
                                "draw": response.draw || 1,
                                "recordsTotal": 0,
                                "recordsFiltered": 0,
                                "data": []
                            });
                        }
                    } else {
                        console.error('❌ Resposta não é um objeto válido:', response);
                        fnCallback({
                            "draw": 1,
                            "recordsTotal": 0,
                            "recordsFiltered": 0,
                            "data": []
                        });
                    }
                },
                'error': function(xhr, status, error) {
                    console.log('Erro AJAX:', error);
                    console.log('Status:', status);
                    console.log('Resposta:', xhr.responseText);
                    console.log('Status HTTP:', xhr.status);

                    // Callback com dados vazios em caso de erro
                    fnCallback({
                        "draw": 1,
                        "recordsTotal": 0,
                        "recordsFiltered": 0,
                        "data": []
                    });
                }
            });
        },
        "aoColumns": [
            null, // Data
            null, // Cliente
            null, // Telefone
            null, // Produto
            null, // Qtd
            {"mRender": currencyFormat}, // Valor Total
            {"mRender": currencyFormat}, // Valor pago
            {"mRender": currencyFormat}, // Saldo a pagar
            null, // Forma de pagamento
            null, // Observação
            {"bSortable": false, "bSearchable": false} // Ações
        ],
        "fnDrawCallback": function() {
            eventFired('fnDrawCallback');
        },
        "oLanguage": {
            "sProcessing": "Carregando dados, aguarde...",
            "sLengthMenu": "Mostrar _MENU_ registros por página",
            "sZeroRecords": "Nenhum orçamento encontrado",
            "sInfo": "Mostrando _START_ a _END_ de _TOTAL_ orçamentos",
            "sInfoEmpty": "Mostrando 0 a 0 de 0 orçamentos",
            "sInfoFiltered": "(filtrado de _MAX_ orçamentos)",
            "sSearch": "Buscar:",
            "oPaginate": {
                "sFirst": "Primeiro",
                "sPrevious": "Anterior",
                "sNext": "Próximo",
                "sLast": "Último"
            }
        }
    });
    } // Fim da função initializeDataTable
});
</script>

<div class="box">
    <div class="box-header">
        <h2 class="blue"><i class="fa-fw fa fa-file-text-o"></i><?= lang('orçamentos'); ?></h2>
        <div class="box-icon">
            <ul class="btn-tasks">
                <li class="dropdown">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#"><i class="icon fa fa-tasks tip" data-placement="left" title="<?= lang("actions") ?>"></i></a>
                    <ul class="dropdown-menu pull-right tasks-menus" role="menu" aria-labelledby="dLabel">
                        <li><a href="<?= site_url('pos/orcamento') ?>"><i class="fa fa-plus-circle"></i> <?= 'Novo Orçamento' ?></a></li>
                        <li class="divider"></li>
                        <li><a href="#" id="excel" data-action="export_excel"><i class="fa fa-file-excel-o"></i> <?= lang('export_to_excel') ?></a></li>
                        <li><a href="#" id="pdf" data-action="export_pdf"><i class="fa fa-file-pdf-o"></i> <?= lang('export_to_pdf') ?></a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <div class="box-content">
        <div class="row">
            <div class="col-lg-12">
                <p class="introtext"><?= lang('list_results'); ?></p>
                <div class="table-responsive">
                    <table id="OrcData" class="table table-bordered table-condensed table-hover table-striped">
                        <thead>
                        <tr class="primary">
                            <th><?= 'Data' ?></th>
                            <th><?= 'Cliente' ?></th>
                            <th><?= 'Telefone' ?></th>
                            <th><?= 'Produto' ?></th>
                            <th><?= 'Qtd' ?></th>
                            <th><?= 'Valor Total' ?></th>
                            <th><?= 'Valor Pago' ?></th>
                            <th><?= 'Saldo a Pagar' ?></th>
                            <th><?= 'Forma de Pagamento' ?></th>
                            <th><?= 'Observação' ?></th>
                            <th style="width:120px;"><?= lang("actions"); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td colspan="11" class="dataTables_empty"><?= lang('loading_data_from_server'); ?></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function(){
    // Exportar para Excel
    $('#excel').click(function(e){
        e.preventDefault();
        window.open('<?= site_url('purchases/export_orcamentos/excel') ?>', '_blank');
    });
    
    // Exportar para PDF
    $('#pdf').click(function(e){
        e.preventDefault();
        window.open('<?= site_url('purchases/export_orcamentos/pdf') ?>', '_blank');
    });
});
</script>
