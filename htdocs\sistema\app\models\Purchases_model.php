<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Purchases_model extends CI_Model
{

    public function __construct() {
		parent::__construct();

	}

	public function getOrcamentoByID($id)
	{
		$q = $this->db->get_where('tec_purchase_orcamentos', array('id' => $id), 1);
		if ($q->num_rows() > 0) {
			return $q->row();
		}
		return FALSE;
	}

	public function getAllOrcamentoItems($orcamento_id)
	{
		$this->db->select('tec_purchase_orcamento_items.*, products.code as product_code, products.name as product_name')
		->join('products', 'products.id=tec_purchase_orcamento_items.product_id', 'left')
		->group_by('tec_purchase_orcamento_items.id')
		->order_by('id', 'asc');
		$q = $this->db->get_where('tec_purchase_orcamento_items', array('orcamento_id' => $orcamento_id));
		if ($q->num_rows() > 0) {
			foreach (($q->result()) as $row) {
				$data[] = $row;
			}
			return $data;
		}
		return FALSE;
	}

	public function addOrcamento($data, $items)
	{
		if ($this->db->insert('tec_purchase_orcamentos', $data)) {
			$orcamento_id = $this->db->insert_id();
			foreach ($items as $item) {
				$item['orcamento_id'] = $orcamento_id;
				$this->db->insert('tec_purchase_orcamento_items', $item);
			}
			return $orcamento_id;
		}
		return false;
	}

	public function updateOrcamento($id, $data, $items)
	{
		if ($this->db->update('tec_purchase_orcamentos', $data, array('id' => $id))) {
			$this->db->delete('tec_purchase_orcamento_items', array('orcamento_id' => $id));
			foreach ($items as $item) {
				$item['orcamento_id'] = $id;
				$this->db->insert('tec_purchase_orcamento_items', $item);
			}
			return true;
		}
		return false;
	}

	public function deleteOrcamento($id)
	{
		if ($this->db->delete('tec_purchase_orcamento_items', array('orcamento_id' => $id)) && $this->db->delete('tec_purchase_orcamentos', array('id' => $id))) {
			return true;
		}
		return FALSE;
	}

    public function getExpenseByID($id) {
        $q = $this->db->get_where('expenses', array('id' => $id), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function addExpense($data = array()) {
        if ($this->db->insert('expenses', $data)) {
            return true;
        }
        return false;
    }

    public function updateExpense($id, $data = array()) {
        if ($this->db->update('expenses', $data, array('id' => $id))) {
            return true;
        }
        return false;
    }

    public function deleteExpense($id) {
        if ($this->db->delete('expenses', array('id' => $id))) {
            return true;
        }
        return FALSE;
    }

}
