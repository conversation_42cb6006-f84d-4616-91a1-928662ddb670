<?php (defined('BASEPATH')) OR exit('No direct script access allowed'); ?>
</div>
<div class="modal" data-easein="flipYIn" id="posModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"></div>
<div id="ajaxCall"><i class="fa fa-spinner fa-pulse"></i></div>
<script type="text/javascript">
    var base_url = '<?=base_url();?>';
    var dateformat = '<?=$Settings->dateformat;?>', timeformat = '<?= $Settings->timeformat ?>';
    var csrf_token_name = '<?= $this->security->get_csrf_token_name(); ?>';
    var csrf_hash = '<?= $this->security->get_csrf_hash(); ?>';
    <?php unset($Settings->protocol, $Settings->smtp_host, $Settings->smtp_user, $Settings->smtp_pass, $Settings->smtp_port, $Settings->smtp_crypto, $Settings->mailpath, $Settings->timezone, $Settings->setting_id, $Settings->default_email, $Settings->version, $Settings->stripe); ?>
    var Settings = <?= json_encode($Settings); ?>;
    $(window).load(function () {
        $('.mm_<?=$m?>').addClass('active');
        $('#<?=$m?>_<?=$v?>').addClass('active');
    });

    $(document).ready(function () {
    $('body').tooltip({
            selector: '.tip'
        });
    });
</script>

<script src="<?= $assets ?>bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/datatables/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/slimScroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/fastclick/fastclick.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/redactor/redactor.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/iCheck/icheck.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/select2/select2.full.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/formvalidation/js/formValidation.popular.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>plugins/formvalidation/js/framework/bootstrap.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/common-libs.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/app.min.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/custom.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/disable-sidebar-hover.js" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/floating-menu.js?<?=date("ymdhis");?>" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/force-click-only-sidebar.js?<?=date("ymdhis");?>" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/pages/all.js?<?=date("ymdhis");?>" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/sidebar-state.js?<?=date("ymdhis");?>" type="text/javascript"></script>
<script src="<?= $assets ?>dist/js/popup-alerts.js?<?=date("ymdhis");?>" type="text/javascript"></script>
<script>
$(document).ready(function() {
    // Initialize popup alerts system
    if (typeof PopupAlerts !== 'undefined') {
        PopupAlerts.init();
    }
});
</script>
<link rel="stylesheet"href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css"/>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<script>
$(document).ready(function () {
Fancybox.bind("[data-fancybox]", {
groupAttr: false,
width: "100%",
height:"100%",
type: "iframe",
});
});
</script>
<style>.fancybox__slide {padding: 3% 1%!important;}</style>
</body>
</html>
