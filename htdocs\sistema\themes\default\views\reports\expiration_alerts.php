<?php (defined('BASEPATH')) OR exit('No direct script access allowed'); ?>

<script type="text/javascript">
    $(document).ready(function() {
        function image(n) {
            if(n !== null) {
                return '<div style="width:32px; margin: 0 auto;"><a href="<?=base_url();?>uploads/'+n+'" class="open-image"><img src="<?=base_url();?>uploads/thumbs/'+n+'" alt="" class="img-responsive"></a></div>';
            }
            return '';
        }
        function method(n) {
            return (n == 0) ? '<span class="label label-primary"><?= lang('inclusive'); ?></span>' : '<span class="label label-warning"><?= lang('exclusive'); ?></span>';
        }
        function tipodeproduto(n) {
            if(n == "standard") {
                return "Produto";
            }else{
                return 'Serviço';
            }
            
        }

        function numberToReal(numero = "", decimal = 2) {
           if(numero=="" || numero == null) return 0;
            return new Intl.NumberFormat('de-DE', { style: 'decimal', currency: 'EUR',minimumFractionDigits: decimal, maximumFractionDigits: decimal }).format(numero);
        }
        
        function formatDate(data) {
            if (data) {
                var date = new Date(data);
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var year = date.getFullYear().toString().slice(-2);
                return month + '/' + year;
            }
            return '-';
        }

        var oTable = $('#fileData').dataTable( {
            "aLengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, '<?= lang('all'); ?>']],
            "aaSorting": [[ 7, "asc" ]], "iDisplayLength": <?= $Settings->rows_per_page ?>,
            'bProcessing': true, 'bServerSide': true,
            'sAjaxSource': '<?= site_url('reports/get_expiration_alerts/') ?>',
            'fnServerData': function (sSource, aoData, fnCallback) {
                aoData.push({
                    "name": "<?= $this->security->get_csrf_token_name() ?>",
                    "value": "<?= $this->security->get_csrf_hash() ?>"
                });

                // Adicionar filtro de data se selecionado
                var dateFilter = $('#date_filter').val();
                if (dateFilter) {
                    aoData.push({
                        "name": "date_filter",
                        "value": dateFilter
                    });
                }

                $.ajax({'dataType': 'json', 'type': 'POST', 'url': sSource, 'data': aoData, 'success': fnCallback});
            },
            "aoColumns": [{"mRender":image}, null, null, {"mRender":tipodeproduto}, null, {"mRender": function ( data, type, row ) { return numberToReal(data, 3); }}, {"mRender": function(data, type, row) {
                if (data) {
                    var date = new Date(data);
                    var month = (date.getMonth() + 1).toString().padStart(2, '0');
                    var year = date.getFullYear().toString().slice(-2);
                    return month + '/' + year;
                }
                return '-';
            }}, {"mRender":currencyFormat}, {"mRender":currencyFormat}, null]
        });

        $('#fileData').on('click', '.open-image', function() {
            var a_href = $(this).attr('href');
            $('#product_image').attr('src',a_href);
            $('#picModal').modal();
            return false;
        });

        $('#fileData').on('click', '.ap', function() {
            var id = $(this).attr('data-id');
            $.get( "<?= site_url('purchases/suggestions'); ?>/"+id )
            .done(function( data ) {
                var item = JSON.parse(data);
                if (get('spoitems')) {
                    var spoitems = JSON.parse(get('spoitems'));
                } else {
                    var spoitems = {};
                }
                var item_id = Settings.item_addition == 1 ? item.item_id : item.id;
                if (spoitems[item_id]) {
                    spoitems[item_id].row.qty = parseFloat(spoitems[item_id].row.qty) + 1;
                } else {
                    spoitems[item_id] = item;
                }
                store('spoitems', JSON.stringify(spoitems));
                $('#custom-alerts').find('.alert').addClass('alert-success');
                $('#custom-alerts').find('.custom-msg').html('<?= lang('po_item_added'); ?> '+spoitems[item_id].label+' = '+spoitems[item_id].row.qty + "/ <a href='<?= site_url('purchases/add'); ?>'>Ver itens para compras</a>");
                $('#custom-alerts').show();
            });
            return false;
        });

        // Carregar data salva do localStorage
        var savedDate = localStorage.getItem('expiration_alerts_date_filter');
        if (savedDate) {
            $('#date_filter').val(savedDate);
        }

        // Event listener para filtro de data
        $('#date_filter').on('change', function() {
            var selectedDate = $(this).val();
            if (selectedDate) {
                localStorage.setItem('expiration_alerts_date_filter', selectedDate);
            } else {
                localStorage.removeItem('expiration_alerts_date_filter');
            }
            oTable.fnDraw();
        });

        // Event listener para limpar filtro
        $('#clear_filter').on('click', function() {
            $('#date_filter').val('');
            localStorage.removeItem('expiration_alerts_date_filter');
            oTable.fnDraw();
        });

    });

</script>
<style type="text/css">
    .table td:first-child { padding: 1px; }
    .table td{ text-align: center; }
</style>
<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary">
                <div class="box-header">
                    <h3 class="box-title">Lista de Produtos com Validade Próxima (3 meses)</h3>
                </div>
                <div class="box-body">
                    <!-- Filtro de Data -->
                    <div class="row" style="margin-bottom: 15px;">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="date_filter">Filtrar por data de validade (a partir de):</label>
                                <div class="input-group">
                                    <input type="date" id="date_filter" class="form-control" placeholder="Selecione uma data">
                                    <span class="input-group-btn">
                                        <button type="button" id="clear_filter" class="btn btn-default" title="Limpar filtro">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </span>
                                </div>
                                <span class="help-block">
                                    <i class="fa fa-info-circle text-info"></i>
                                    <strong>Esta data também controla os popups de validade:</strong>
                                    Apenas produtos que estão vencendo/venceram a partir desta data aparecerão nos popups.
                                </span>
                            </div>
                        </div>
                    </div>
                        <div class="table-responsive">
                        <table id="fileData" class="table table-striped table-bordered table-hover" style="margin-bottom:5px;">
                            <thead>
                            <tr class="active">
                                <th style="max-width:30px;"><?= lang("image"); ?></th>
                                <th class="col-xs-1"><?= lang("code"); ?></th>
                                <th><?= lang("name"); ?></th>
                                <th class="col-xs-1"><?= lang("type"); ?></th>
                                <th class="col-xs-1"><?= lang("category"); ?></th>
                                <th class="col-xs-1"><?= lang("quantity"); ?></th>
                                <th class="col-xs-1">Validade</th>
                                <th class="col-xs-1"><?= lang("cost"); ?></th>
                                <th class="col-xs-1"><?= lang("price"); ?></th>
                                <th style="width:35px;"><?= lang("actions"); ?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="12" class="dataTables_empty"><?= lang('loading_data_from_server'); ?></td>
                            </tr>
                            </tbody>
                        </table>
                        </div>

                        <div class="modal fade" id="picModal" tabindex="-1" role="dialog" aria-labelledby="picModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-sm">
                                <div class="modal-content">
                                    <div class="modal-body text-center">
                                        <img id="product_image" src="" alt="" />
                                    </div>
                                </div>
                            </div>
                        </div>

                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
    </div>
</section>
