<?php if (!defined('BASEPATH')) exit('No direct script access allowed'); ?>
<?php if (!empty($orcamento)) { ?>
<div class="box">
    <div class="box-header">
        <h2 class="blue"><i class="fa-fw fa fa-file-text-o"></i>Visualizar Orçamento #<?= $orcamento->id ?></h2>
        <div class="box-icon">
            <ul class="btn-tasks">
                <li class="dropdown">
                    <a href="<?= site_url('purchases/orcamentos') ?>" class="btn btn-primary btn-sm">
                        <i class="fa fa-arrow-left"></i> Voltar para Lista
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="box-content">
        <div class="row">
            <div class="col-lg-12">
                
                <!-- Informações do Orçamento -->
                <div class="well well-sm">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Informações do Orçamento</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Referência:</strong></td>
                                    <td><?= $orcamento->reference ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Data:</strong></td>
                                    <td><?= date('d/m/Y H:i', strtotime($orcamento->date)) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Cliente:</strong></td>
                                    <td><?= $orcamento->supplier_name ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php if (isset($orcamento->status)) { ?>
                                            <span class="label label-<?= $orcamento->status == 'pending' ? 'warning' : 'success' ?>">
                                                <?= $orcamento->status == 'pending' ? 'Pendente' : 'Finalizado' ?>
                                            </span>
                                        <?php } else { ?>
                                            <span class="label label-warning">Pendente</span>
                                        <?php } ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h4>Informações Financeiras</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Subtotal:</strong></td>
                                    <td>R$ <?= number_format($orcamento->total, 2, ',', '.') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Desconto:</strong></td>
                                    <td>R$ <?= number_format($orcamento->total_discount, 2, ',', '.') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Total Geral:</strong></td>
                                    <td><strong>R$ <?= number_format($orcamento->grand_total, 2, ',', '.') ?></strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Forma de Pagamento:</strong></td>
                                    <td><?= $orcamento->forma_pagamento ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Valor Pago:</strong></td>
                                    <td>R$ <?= number_format($orcamento->valor_pago, 2, ',', '.') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Saldo a Pagar:</strong></td>
                                    <td><strong>R$ <?= number_format(($orcamento->grand_total - $orcamento->valor_pago), 2, ',', '.') ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if (!empty($orcamento->note)) { ?>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>Observações</h4>
                            <p><?= nl2br($orcamento->note) ?></p>
                        </div>
                    </div>
                    <?php } ?>
                </div>

                <!-- Itens do Orçamento -->
                <h4>Itens do Orçamento</h4>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Código</th>
                                <th>Quantidade</th>
                                <th>Preço Unitário</th>
                                <th>Desconto</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $total_items = 0;
                            $total_quantity = 0;
                            $total_amount = 0;
                            
                            if (!empty($items)) {
                                foreach ($items as $item) {
                                    $total_items++;
                                    $total_quantity += $item->quantity;
                                    $total_amount += $item->subtotal;
                            ?>
                            <tr>
                                <td><?= $item->product_name ? $item->product_name : 'Produto #' . $item->product_id ?></td>
                                <td><?= $item->product_code ? $item->product_code : '-' ?></td>
                                <td><?= number_format($item->quantity, 3, ',', '.') ?></td>
                                <td>R$ <?= number_format($item->unit_cost, 2, ',', '.') ?></td>
                                <td>R$ <?= number_format($item->item_discount, 2, ',', '.') ?></td>
                                <td>R$ <?= number_format($item->subtotal, 2, ',', '.') ?></td>
                            </tr>
                            <?php 
                                }
                            } else { 
                            ?>
                            <tr>
                                <td colspan="6" class="text-center">Nenhum item encontrado</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                        <?php if (!empty($items)) { ?>
                        <tfoot>
                            <tr style="background-color: #f5f5f5; font-weight: bold;">
                                <td>TOTAL</td>
                                <td><?= $total_items ?> item(s)</td>
                                <td><?= number_format($total_quantity, 3, ',', '.') ?></td>
                                <td colspan="2"></td>
                                <td>R$ <?= number_format($total_amount, 2, ',', '.') ?></td>
                            </tr>
                        </tfoot>
                        <?php } ?>
                    </table>
                </div>

                <!-- Ações -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="btn-group">
                            <a href="<?= site_url('purchases/orcamentos') ?>" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> Voltar
                            </a>
                            <a href="<?= site_url('purchases/convert_orcamento/' . $orcamento->id) ?>" class="btn btn-success">
                                <i class="fa fa-shopping-cart"></i> Converter em Compra
                            </a>
                            <a href="<?= site_url('purchases/delete_orcamento/' . $orcamento->id) ?>" 
                               onclick="return confirm('Tem certeza que deseja excluir este orçamento?')" 
                               class="btn btn-danger">
                                <i class="fa fa-trash-o"></i> Excluir
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<?php } else { ?>
<div class="box">
    <div class="box-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-danger">
                    <h4>Orçamento não encontrado!</h4>
                    <p>O orçamento solicitado não foi encontrado no sistema.</p>
                    <a href="<?= site_url('purchases/orcamentos') ?>" class="btn btn-primary">
                        <i class="fa fa-arrow-left"></i> Voltar para Lista
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php } ?>

<style>
.table-borderless td {
    border: none !important;
    padding: 5px 10px;
}
.well h4 {
    margin-top: 0;
    color: #333;
}
</style>
