<?php
$v = "?v=1";
?>
<script>
$(document).ready(function () {

    function eventFired(){
       var v_total = 0;
       var v_pago = 0;
       var v_saldo = 0;

       $('#OrcData tbody tr:not(.totals-row)').each(function() {
           // Valor Total (coluna 6)
           v = $(this).find("td:nth-child(6)").text().replace(".", "").replace(",", ".");
           v_total += parseFloat((v=="" || v==0 || v==null)? 0: v);

           // Valor Pago (coluna 7)
           v = $(this).find("td:nth-child(7)").text().replace(".", "").replace(",", ".");
           v_pago += parseFloat((v=="" || v==0 || v==null)? 0: v);

           // Saldo (coluna 8)
           v = $(this).find("td:nth-child(8)").text().replace(".", "").replace(",", ".");
           v_saldo += parseFloat((v=="" || v==0|| v==null)? 0: v);
       });

       // Remover linha de totais anterior
       $('#OrcData tbody .totals-row').remove();
       
       // Adicionar nova linha de totais
       $('#OrcData tbody').append('<tr class="totals-row" style="background-color: #f5f5f5; font-weight: bold;"><td></td><td></td><td></td><td></td><td></td><td><div style="text-align:right">'+numberToReal(v_total)+'</div></td><td><div style="text-align:right">'+numberToReal(v_pago)+'</div></td><td><div style="text-align:right">'+numberToReal(v_saldo)+'</div></td><td></td><td></td><td></td></tr>');
    }
    
    function numberToReal(numero = "", decimal = 2) {
        if(numero=="" || numero == null) return "0,00";
        return new Intl.NumberFormat('pt-BR', { 
            style: 'decimal', 
            minimumFractionDigits: decimal, 
            maximumFractionDigits: decimal 
        }).format(numero);
    }

    // Implementação manual sem DataTables
    var currentPage = 0;
    var pageSize = 50;
    var totalRecords = 0;
    
    console.log('Inicializando tabela manual...');
    loadTableData(0, 50);
    
    function loadTableData(start, length) {
        // Mostrar loading
        $('#OrcData tbody').html('<tr><td colspan="11" class="text-center"><i class="fa fa-spinner fa-spin"></i> Carregando dados...</td></tr>');
        
        $.ajax({
            url: '<?= site_url('purchases/get_orcamentos' . $v) ?>',
            type: 'POST',
            data: {
                'draw': 1,
                'start': start,
                'length': length,
                '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
            },
            dataType: 'json',
            success: function(response) {
                console.log('✅ Dados carregados:', response);
                
                if (response && response.data && Array.isArray(response.data)) {
                    totalRecords = response.recordsTotal || 0;
                    
                    // Limpar tabela
                    $('#OrcData tbody').empty();
                    
                    if (response.data.length > 0) {
                        // Adicionar dados
                        response.data.forEach(function(row) {
                            var tr = '<tr>';
                            row.forEach(function(cell, index) {
                                tr += '<td>' + cell + '</td>';
                            });
                            tr += '</tr>';
                            $('#OrcData tbody').append(tr);
                        });
                        
                        // Executar totalizações
                        eventFired();
                    } else {
                        $('#OrcData tbody').html('<tr><td colspan="11" class="text-center text-muted">Nenhum orçamento encontrado</td></tr>');
                    }
                    
                    // Atualizar informações de paginação
                    updatePaginationInfo(start, length, totalRecords);
                    
                } else {
                    $('#OrcData tbody').html('<tr><td colspan="11" class="text-center text-danger">Erro: Dados inválidos recebidos</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Erro ao carregar dados:', error);
                $('#OrcData tbody').html('<tr><td colspan="11" class="text-center text-danger">Erro ao carregar dados: ' + error + '</td></tr>');
            }
        });
    }
    
    function updatePaginationInfo(start, length, total) {
        var end = Math.min(start + length, total);
        var info = 'Mostrando ' + (start + 1) + ' até ' + end + ' de ' + total + ' registros';
        
        // Criar div de informações se não existir
        if ($('#table-info').length === 0) {
            $('#OrcData_wrapper').append('<div id="table-info" style="margin-top: 10px; float: left; color: #666;">' + info + '</div>');
        } else {
            $('#table-info').text(info);
        }
        
        // Criar controles de paginação
        createPaginationControls(start, length, total);
    }
    
    function createPaginationControls(start, length, total) {
        var totalPages = Math.ceil(total / length);
        var currentPageNum = Math.floor(start / length) + 1;
        
        if ($('#pagination-controls').length === 0) {
            $('#OrcData_wrapper').append('<div id="pagination-controls" style="margin-top: 10px; float: right;"></div>');
        }
        
        var controls = '';
        
        if (currentPageNum > 1) {
            controls += '<button onclick="loadTableData(' + (start - length) + ', ' + length + ')" class="btn btn-sm btn-default" style="margin-right: 5px;"><i class="fa fa-chevron-left"></i> Anterior</button>';
        }
        
        controls += '<span style="margin: 0 10px; color: #666;">Página ' + currentPageNum + ' de ' + totalPages + '</span>';
        
        if (currentPageNum < totalPages) {
            controls += '<button onclick="loadTableData(' + (start + length) + ', ' + length + ')" class="btn btn-sm btn-default" style="margin-left: 5px;">Próxima <i class="fa fa-chevron-right"></i></button>';
        }
        
        $('#pagination-controls').html(controls);
    }
    
    // Tornar funções globais para os botões de paginação
    window.loadTableData = loadTableData;
});
</script>

<div class="box">
    <div class="box-header">
        <h2 class="blue"><i class="fa-fw fa fa-file-text-o"></i><?= lang('orçamentos'); ?></h2>
        <div class="box-icon">
            <ul class="btn-tasks">
                <li class="dropdown">
                    <a data-toggle="dropdown" class="dropdown-toggle" href="#"><i class="icon fa fa-tasks tip" data-placement="left" title="<?= lang("actions") ?>"></i></a>
                    <ul class="dropdown-menu pull-right tasks-menus" role="menu" aria-labelledby="dLabel">
                        <li><a href="<?= site_url('pos/orcamento') ?>"><i class="fa fa-plus-circle"></i> <?= 'Novo Orçamento' ?></a></li>
                        <li class="divider"></li>
                        <li><a href="#" id="excel" data-action="export_excel"><i class="fa fa-file-excel-o"></i> <?= lang('export_to_excel') ?></a></li>
                        <li><a href="#" id="pdf" data-action="export_pdf"><i class="fa fa-file-pdf-o"></i> <?= lang('export_to_pdf') ?></a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
    <div class="box-content">
        <div class="row">
            <div class="col-lg-12">
                <div id="OrcData_wrapper" class="dataTables_wrapper form-inline" role="grid">
                    <table id="OrcData" class="table table-bordered table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Cliente</th>
                                <th>Telefone</th>
                                <th>Produto</th>
                                <th>Qtd</th>
                                <th>Valor Total</th>
                                <th>Valor Pago</th>
                                <th>Saldo a Pagar</th>
                                <th>Forma de Pagamento</th>
                                <th>Observação</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="11" class="text-center">
                                    <i class="fa fa-spinner fa-spin"></i> Carregando dados...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="clear: both; margin-top: 10px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function(){
    // Exportar para Excel
    $('#excel').click(function(e){
        e.preventDefault();
        window.open('<?= site_url('purchases/export_orcamentos/excel') ?>', '_blank');
    });
    
    // Exportar para PDF
    $('#pdf').click(function(e){
        e.preventDefault();
        window.open('<?= site_url('purchases/export_orcamentos/pdf') ?>', '_blank');
    });
});
</script>

<style>
#OrcData tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}
#OrcData tbody tr:nth-child(odd) {
    background-color: #ffffff;
}
#OrcData tbody tr:hover {
    background-color: #f5f5f5;
}
.totals-row:hover {
    background-color: #f5f5f5 !important;
}
</style>
