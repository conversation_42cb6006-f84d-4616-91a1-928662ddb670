<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Site extends CI_Model
{

    public function __construct()
    {
        parent::__construct();

    }

    public function is_connected()
    {
        try {
            $connected = @fsockopen("www.google.com", 80);
            //website, port  (try 80 or 443)
            if ($connected){
                $is_conn = true; //action when connected
                fclose($connected);
            }else{
                $is_conn = false; //action in connection failure
            }
            return $is_conn;
        } catch (\Throwable $th) {
            //throw $th;
            return false;
        }
    }

    public function getTotalNF() {
        $year = date("Y");
        $month = date("m");
        $myQuery = "SELECT * FROM ".$this->db->dbprefix('notasfiscais')."
		WHERE DATE_FORMAT( data,  '%Y-%m' ) =  '{$year}-{$month}'";
		$q = $this->db->query($myQuery, false);
		if($q->num_rows() > 0) {
            return $q->num_rows();
        }else{
            return 0;
        }
    }

    public function getTotalNFC() {
        $year = date("Y");
        $month = date("m");
        $myQuery = "SELECT * FROM ".$this->db->dbprefix('sales')."
		WHERE nf_numero IS NOT NULL AND DATE_FORMAT( date,  '%Y-%m' ) =  '{$year}-{$month}'";
		$q = $this->db->query($myQuery, false);
		if($q->num_rows() > 0) {
            return $q->num_rows();
        }else{
            return 0;
        }
    }


    public function getTotalUsuario() {
        return $this->db->count_all_results('users');
    }


    public function getQtyAlerts() {
        $this->db->where('quantity < alert_quantity', NULL, FALSE);
        return $this->db->count_all_results('products');
    }

    public function getExpirationAlerts() {
        // Get current month and year
        $current_month = date('m');
        $current_year = date('Y');

        // Calculate month and year 3 months from now
        $expiry_month = $current_month + 3;
        $expiry_year = $current_year;

        // Adjust if month > 12
        if ($expiry_month > 12) {
            $expiry_month = $expiry_month - 12;
            $expiry_year++;
        }

        // Format as YYYY-MM-DD for the last day of the month
        $start_date = date('Y-m-d'); // Today
        $end_date = sprintf('%04d-%02d-%02d', $expiry_year, $expiry_month, date('t', strtotime($expiry_year.'-'.$expiry_month.'-01')));

        $this->db->where('validade !=', NULL);
        $this->db->where('validade !=', '0000-00-00');
        $this->db->where('validade <=', $end_date);
        $this->db->where('validade >=', $start_date);
        return $this->db->count_all_results('products');
    }

    public function getSettings()
    {
        $q = $this->db->get('settings');
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function getSettingValue($key)
    {
        try {
            // Check if column exists first
            if (!$this->db->field_exists($key, 'settings')) {
                return null;
            }

            $q = $this->db->select($key)->get('settings');
            if ($q->num_rows() > 0) {
                $row = $q->row();
                return isset($row->$key) ? $row->$key : null;
            }
            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    public function updateSetting($key, $value)
    {
        // Check if column exists, if not add it
        if (!$this->db->field_exists($key, 'settings')) {
            $this->db->query("ALTER TABLE " . $this->db->dbprefix('settings') . " ADD COLUMN `$key` VARCHAR(255) NULL");
        }

        return $this->db->update('settings', array($key => $value), array('setting_id' => 1));
    }

    public function getAllCustomers()
    {
       // $q = $this->db->get('customers');
        $q = $this->db->get_where('customers', array('tipo_cad !=' => 0));
        if($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function getAllmeiopagamento()
    {
        $q = $this->db->get('meiopagamento');
        if($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                // desativamos o gift_card
                if($row->cod!="gift_card")
                    $data[] = $row;

            }
            return $data;
        }
        return FALSE;
    }

    public function getAllSuppliers()
    {
        $q = $this->db->get('suppliers');
        if($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function getAllUsers()
    {
        $this->db->select("users.id as id, first_name, last_name, email, company, " . $this->db->dbprefix('groups') . ".name as group, active")
            ->join('groups', 'users.group_id=groups.id', 'left')
            ->group_by('users.id');
        $q = $this->db->get('users');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function getUser($id = NULL)
    {
        if (!$id) {
            $id = $this->session->userdata('user_id');
        }
        $q = $this->db->get_where('users', array('id' => $id), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function getUser2($id = NULL)
    {
        if (!$id) {
            $id = $this->session->userdata('user_id');
        }
        $this->db->select('first_name, last_name');
	$this->db->from('users');
	$this->db->where('id', $id);
	$q = $this->db->get();

        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function getProductByID($id)
    {
        // Depuração para verificar a consulta SQL
        $this->db->select('*');
        $this->db->from('products');
        $this->db->where('id', $id);
        $query = $this->db->get_compiled_select();
        error_log('Consulta SQL para recuperar o produto: ' . $query);

        $q = $this->db->get_where('products', array('id' => $id), 1);
        if ($q->num_rows() > 0) {
            $result = $q->row();

            // Formatar o campo validade de YYYY-MM-DD para mm/aa
            if (!empty($result->validade) && $result->validade != '0000-00-00') {
                $date = new DateTime($result->validade);
                $result->validade = $date->format('m/y');
            }

            // Depuração para verificar o campo validade
            error_log('Produto recuperado do banco: ' . print_r($result, true));
            return $result;
        }
        return FALSE;
    }

    public function getAllCategories()
    {
        $this->db->order_by('code');
        $q = $this->db->get('categories');
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function getCategoryByID($id)
    {
        $q = $this->db->get_where('categories', array('id' => $id), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function getCategoryByCode($code)
    {
        $q = $this->db->get_where('categories', array('code' => $code), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

     public function getCategoryByName($name)
    {
        if(empty($name)){ return FALSE; }
        $q = $this->db->get_where('categories', array('name' => $name), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }


    public function getGiftCard($no)
    {
        $q = $this->db->get_where('gift_cards', array('card_no' => $no), 1);
        if ($q->num_rows() > 0) {
            return $q->row();
        }
        return FALSE;
    }

    public function getUpcomingEvents()
    {
        $dt = date('Y-m-d');
        $this->db->where('date >=', $dt)->order_by('date')->limit(5);
        if ($this->Settings->restrict_calendar) {
            $q = $this->db->get_where('calendar', array('user_id' => $this->session->userdata('iser_id')));
        } else {
            $q = $this->db->get('calendar');
        }
        if ($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

    public function getUserGroup($user_id = NULL) {
        if($group_id = $this->getUserGroupID($user_id)) {
            $q = $this->db->get_where('groups', array('id' => $group_id), 1);
            if ($q->num_rows() > 0) {
                return $q->row();
            }
        }
        return FALSE;
    }

    public function getUserGroupID($user_id = NULL) {
        if($user = $this->getUser($user_id)) {
            return $user->group_id;
        }
        return FALSE;
    }

    public function getUserSuspenedSales()
    {
        //$user_id = $this->session->userdata('user_id');
        $this->db->select('id, date, customer_name, hold_ref, grand_total, (grand_total - paid) as faltatotal')
        ->order_by('id desc');
        //->limit(10);
        $q = $this->db->get_where('suspended_sales'); // , array('created_by' => $user_id)
        if($q->num_rows() > 0) {
            foreach (($q->result()) as $row) {
                $data[] = $row;
            }
            return $data;
        }
        return FALSE;
    }

}