/**
 * Popup Alerts System
 * Manages stock and expiration alerts popups
 */

var PopupAlerts = {
    currentPopup: null,
    alertQueue: [],
    isProcessing: false,
    checkInterval: null,

    init: function() {
        this.checkForAlerts();
        // Check for new alerts every 5 minutes
        this.checkInterval = setInterval(() => {
            this.checkForAlerts();
        }, 5 * 60 * 1000);
    },

    checkForAlerts: function() {
        if (this.isProcessing) return;

        this.isProcessing = true;

        $.ajax({
            url: base_url + 'reports/get_popup_alerts',
            type: 'POST',
            dataType: 'json',
            data: {
                [csrf_token_name]: csrf_hash,
                alerts_date_filter: localStorage.getItem('alerts_date_filter'),
                expiration_alerts_date_filter: localStorage.getItem('expiration_alerts_date_filter')
            },
            success: (response) => {
                if (response.success && response.alerts && response.alerts.length > 0) {
                    this.alertQueue = response.alerts;
                    this.showNextAlert();
                }
                this.isProcessing = false;
            },
            error: () => {
                this.isProcessing = false;
            }
        });
    },

    showNextAlert: function() {
        if (this.currentPopup || this.alertQueue.length === 0) return;

        const alert = this.alertQueue.shift();
        this.showPopup(alert);
    },

    showPopup: function(alert) {
        const popupHtml = this.createPopupHtml(alert);
        
        // Remove existing popup if any
        $('.alert-popup-overlay').remove();
        
        // Add popup to body
        $('body').append(popupHtml);
        
        // Show popup with animation
        $('.alert-popup-overlay').fadeIn(300);
        
        this.currentPopup = alert;
        
        // Bind events
        this.bindPopupEvents(alert);
    },

    createPopupHtml: function(alert) {
        const isExpiration = alert.alert_type === 'expiration';
        const headerClass = isExpiration ? 'expiration' : '';
        const icon = isExpiration ? 'fa-calendar-times-o' : 'fa-exclamation-triangle';
        const title = isExpiration ? 'Alerta de Validade' : 'Alerta de Estoque';
        
        let message = '';
        if (isExpiration) {
            const expirationDate = new Date(alert.validade);
            const formattedDate = expirationDate.toLocaleDateString('pt-BR');
            message = `Este produto vence em ${formattedDate}`;
        } else {
            message = 'Este produto está com estoque zerado';
        }

        const imageUrl = alert.image && alert.image !== 'no_image.png'
            ? base_url + 'uploads/thumbs/' + alert.image
            : base_url + 'uploads/no_image.png';

        return `
            <div class="alert-popup-overlay">
                <div class="alert-popup">
                    <div class="alert-popup-header ${headerClass}">
                        <i class="fa ${icon} alert-icon"></i>
                        <h3>${title}</h3>
                    </div>
                    <div class="alert-popup-body">
                        <div class="product-info">
                            <img src="${imageUrl}" alt="${alert.name}" class="product-image" onerror="this.src='${base_url}uploads/no_image.png'">
                            <div class="product-details">
                                <h4>${alert.name}</h4>
                                <p><strong>Código:</strong> ${alert.code}</p>
                                ${isExpiration ? `<p><strong>Validade:</strong> ${new Date(alert.validade).toLocaleDateString('pt-BR')}</p>` : ''}
                                ${!isExpiration ? `<p><strong>Quantidade:</strong> ${alert.quantity}</p>` : ''}
                            </div>
                        </div>
                        <div class="alert-message ${headerClass}">
                            <p>${message}</p>
                        </div>
                        <div class="popup-actions">
                            <button class="popup-btn popup-btn-dismiss" data-action="dismiss">
                                <i class="fa fa-times"></i>
                                Lembrar em 12h
                            </button>
                            <button class="popup-btn popup-btn-accept" data-action="accept">
                                <i class="fa fa-check"></i>
                                Não mostrar mais
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    bindPopupEvents: function(alert) {
        // Close on overlay click
        $('.alert-popup-overlay').on('click', (e) => {
            if (e.target === e.currentTarget) {
                this.handleAction(alert, 'dismiss');
            }
        });

        // Handle button clicks
        $('.popup-btn').on('click', (e) => {
            const action = $(e.currentTarget).data('action');
            this.handleAction(alert, action);
        });

        // ESC key to dismiss
        $(document).on('keydown.popup', (e) => {
            if (e.keyCode === 27) { // ESC key
                this.handleAction(alert, 'dismiss');
            }
        });
    },

    handleAction: function(alert, action) {
        const $button = $(`.popup-btn[data-action="${action}"]`);
        const originalHtml = $button.html();
        
        // Show loading
        $button.html('<span class="popup-loading"></span> Processando...');
        $button.prop('disabled', true);

        const actionType = action === 'accept' ? 'dismissed_v' : 'dismissed_x';

        $.ajax({
            url: base_url + 'reports/record_popup_action',
            type: 'POST',
            dataType: 'json',
            data: {
                product_id: alert.id,
                popup_type: alert.alert_type,
                action: actionType,
                [csrf_token_name]: csrf_hash
            },
            success: (response) => {
                if (response.success) {
                    this.closePopup();
                    
                    // Show next alert after a short delay
                    setTimeout(() => {
                        this.showNextAlert();
                    }, 500);
                } else {
                    alert('Erro ao processar ação. Tente novamente.');
                    $button.html(originalHtml);
                    $button.prop('disabled', false);
                }
            },
            error: () => {
                alert('Erro de conexão. Tente novamente.');
                $button.html(originalHtml);
                $button.prop('disabled', false);
            }
        });
    },

    closePopup: function() {
        $('.alert-popup-overlay').fadeOut(300, function() {
            $(this).remove();
        });
        
        $(document).off('keydown.popup');
        this.currentPopup = null;
    },

    destroy: function() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
        this.closePopup();
        this.alertQueue = [];
    }
};

// Initialize when document is ready
$(document).ready(function() {
    // Only initialize if we have the required variables
    if (typeof base_url !== 'undefined' && typeof csrf_token_name !== 'undefined') {
        PopupAlerts.init();
    }
});

// Clean up on page unload
$(window).on('beforeunload', function() {
    PopupAlerts.destroy();
});
