/* Popup Alerts CSS */
.alert-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none;
}

.alert-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    z-index: 10000;
    animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.alert-popup-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    text-align: center;
    position: relative;
}

.alert-popup-header.expiration {
    background: linear-gradient(135deg, #ffa726, #ff9800);
}

.alert-popup-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
}

.alert-popup-header .alert-icon {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.alert-popup-body {
    padding: 30px;
    text-align: center;
}

.product-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    object-fit: cover;
    margin-right: 20px;
    border: 3px solid #f0f0f0;
}

.product-details h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 20px;
    font-weight: bold;
}

.product-details p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.alert-message {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    border-left: 5px solid #ff6b6b;
}

.alert-message.expiration {
    border-left-color: #ffa726;
}

.alert-message p {
    margin: 0;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

.popup-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.popup-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 120px;
    justify-content: center;
}

.popup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.popup-btn-dismiss {
    background: #dc3545;
    color: white;
}

.popup-btn-dismiss:hover {
    background: #c82333;
}

.popup-btn-accept {
    background: #28a745;
    color: white;
}

.popup-btn-accept:hover {
    background: #218838;
}

.popup-btn i {
    font-size: 18px;
}

/* Multiple popups container */
.popup-queue {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
}

.popup-queue-item {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
    padding: 15px;
    max-width: 300px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.popup-queue-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.popup-queue-item.stock-alert {
    border-left: 5px solid #ff6b6b;
}

.popup-queue-item.expiration-alert {
    border-left: 5px solid #ffa726;
}

.queue-item-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.queue-item-image {
    width: 40px;
    height: 40px;
    border-radius: 5px;
    object-fit: cover;
}

.queue-item-text {
    flex: 1;
}

.queue-item-text h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #333;
}

.queue-item-text p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
    .alert-popup {
        width: 95%;
        margin: 20px;
    }
    
    .product-info {
        flex-direction: column;
        text-align: center;
    }
    
    .product-image {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .popup-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .popup-btn {
        width: 100%;
    }
}

/* Loading animation */
.popup-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
