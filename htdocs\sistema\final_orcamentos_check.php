<?php
// Verificação final do sistema de orçamentos

require_once 'config.php';

echo "<h2>Verificação Final - Sistema de Orçamentos</h2>\n";

try {
    $pdo = new PDO("mysql:host=$PDV_HOST;dbname=$PDV_BASE;charset=utf8", $PDV_USUARIO, $PDV_SENHA);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>✅ Status das Tabelas:</h3>\n";
    
    // Verificar tabela principal
    $stmt = $pdo->prepare("SHOW TABLE STATUS LIKE 'tec_purchase_orcamentos'");
    $stmt->execute();
    $main_table = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($main_table) {
        echo "<p><strong>Tabela Principal:</strong> ✅ Existe</p>\n";
        echo "<p><strong>Engine:</strong> " . $main_table['Engine'] . "</p>\n";
        echo "<p><strong>Auto_increment:</strong> " . $main_table['Auto_increment'] . "</p>\n";
        echo "<p><strong>Rows:</strong> " . $main_table['Rows'] . "</p>\n";
    } else {
        echo "<p><strong>Tabela Principal:</strong> ❌ Não existe</p>\n";
    }
    
    // Verificar tabela de itens
    $stmt = $pdo->prepare("SHOW TABLE STATUS LIKE 'tec_purchase_orcamento_items'");
    $stmt->execute();
    $items_table = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($items_table) {
        echo "<p><strong>Tabela de Itens:</strong> ✅ Existe</p>\n";
        echo "<p><strong>Engine:</strong> " . $items_table['Engine'] . "</p>\n";
        echo "<p><strong>Auto_increment:</strong> " . $items_table['Auto_increment'] . "</p>\n";
        echo "<p><strong>Rows:</strong> " . $items_table['Rows'] . "</p>\n";
    } else {
        echo "<p><strong>Tabela de Itens:</strong> ❌ Não existe</p>\n";
    }
    
    echo "<h3>📊 Dados Existentes:</h3>\n";
    
    if ($main_table) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM tec_purchase_orcamentos");
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Total de Orçamentos:</strong> " . $count['total'] . "</p>\n";
        
        if ($count['total'] > 0) {
            $stmt = $pdo->prepare("
                SELECT 
                    id, 
                    reference, 
                    supplier_name, 
                    grand_total, 
                    forma_pagamento, 
                    valor_pago,
                    (grand_total - valor_pago) as saldo,
                    status,
                    DATE_FORMAT(date, '%d/%m/%Y %H:%i') as data_formatada
                FROM tec_purchase_orcamentos 
                ORDER BY id DESC 
                LIMIT 10
            ");
            $stmt->execute();
            $orcamentos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Últimos 10 Orçamentos:</h4>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background: #f2f2f2;'>";
            echo "<th>ID</th><th>Referência</th><th>Cliente</th><th>Data</th><th>Total</th><th>Forma Pagto</th><th>Valor Pago</th><th>Saldo</th><th>Status</th>";
            echo "</tr>\n";
            
            foreach ($orcamentos as $orc) {
                $saldo_color = $orc['saldo'] > 0 ? 'color: red;' : 'color: green;';
                echo "<tr>";
                echo "<td>" . $orc['id'] . "</td>";
                echo "<td>" . $orc['reference'] . "</td>";
                echo "<td>" . $orc['supplier_name'] . "</td>";
                echo "<td>" . $orc['data_formatada'] . "</td>";
                echo "<td>R$ " . number_format($orc['grand_total'], 2, ',', '.') . "</td>";
                echo "<td>" . ($orc['forma_pagamento'] ?? 'N/A') . "</td>";
                echo "<td>R$ " . number_format($orc['valor_pago'], 2, ',', '.') . "</td>";
                echo "<td style='$saldo_color'>R$ " . number_format($orc['saldo'], 2, ',', '.') . "</td>";
                echo "<td>" . $orc['status'] . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    }
    
    echo "<h3>🔗 Links do Sistema:</h3>\n";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h4>Páginas Principais:</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='purchases/orcamentos' target='_blank' style='font-weight: bold;'>📋 Lista de Orçamentos</a> - Visualizar todos os orçamentos</li>\n";
    echo "<li><a href='pos/orcamento' target='_blank' style='font-weight: bold;'>➕ Criar Orçamento</a> - Nova página de orçamento no POS</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>🧹 Limpeza de Arquivos:</h3>\n";
    
    $debug_files = array(
        'debug_orcamentos.php',
        'test_orcamento_insert.php',
        'create_new_orcamentos_table.php',
        'cleanup_debug.php',
        'final_orcamentos_check.php'
    );
    
    echo "<p>Arquivos de debug encontrados:</p>\n";
    echo "<ul>\n";
    foreach ($debug_files as $file) {
        if (file_exists($file)) {
            echo "<li>$file - <a href='#' onclick=\"if(confirm('Excluir $file?')) { window.location.href='?delete=$file'; }\">🗑️ Excluir</a></li>\n";
        }
    }
    echo "</ul>\n";
    
    if (isset($_GET['delete'])) {
        $file_to_delete = $_GET['delete'];
        if (in_array($file_to_delete, $debug_files) && file_exists($file_to_delete)) {
            if (unlink($file_to_delete)) {
                echo "<p style='color: green;'>✅ Arquivo $file_to_delete excluído!</p>\n";
                echo "<script>setTimeout(function(){ window.location.href = window.location.pathname; }, 1500);</script>\n";
            } else {
                echo "<p style='color: red;'>❌ Erro ao excluir $file_to_delete</p>\n";
            }
        }
    }
    
    echo "<h3>📋 Funcionalidades Implementadas:</h3>\n";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Sistema Completo de Orçamentos</h4>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li><strong>Criação de Orçamentos:</strong> Interface moderna no POS</li>\n";
    echo "<li><strong>Salvamento Seguro:</strong> Validação e tratamento de erros</li>\n";
    echo "<li><strong>Listagem Completa:</strong> DataTable com paginação de 50 registros</li>\n";
    echo "<li><strong>Colunas Solicitadas:</strong> Data, Cliente, Telefone, Qtd itens, Valor Total, Forma Pagamento, Valor Pago, Saldo, Ações</li>\n";
    echo "<li><strong>Visualização Detalhada:</strong> Cupom completo do orçamento</li>\n";
    echo "<li><strong>Conversão para Compras:</strong> Botão para transformar orçamento em compra</li>\n";
    echo "<li><strong>Exclusão Segura:</strong> Remoção com confirmação</li>\n";
    echo "<li><strong>Formatação Brasileira:</strong> Moeda e datas no padrão nacional</li>\n";
    echo "<li><strong>Banco de Dados Otimizado:</strong> Tabelas InnoDB com chaves estrangeiras</li>\n";
    echo "<li><strong>AUTO_INCREMENT Funcional:</strong> IDs sequenciais corretos</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>🎯 Como Usar:</h3>\n";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<ol style='color: #856404;'>\n";
    echo "<li><strong>Criar Orçamento:</strong> Acesse <code>pos/orcamento</code>, preencha os dados do cliente, adicione produtos e clique em 'Salvar Orçamento'</li>\n";
    echo "<li><strong>Visualizar Orçamentos:</strong> Acesse <code>purchases/orcamentos</code> para ver a lista completa</li>\n";
    echo "<li><strong>Ver Detalhes:</strong> Clique no ícone de visualização (👁️) para ver o cupom completo</li>\n";
    echo "<li><strong>Converter em Compra:</strong> Use o botão do carrinho (🛒) para transformar o orçamento em compra</li>\n";
    echo "<li><strong>Excluir:</strong> Use o botão vermelho (🗑️) para remover orçamentos</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: #721c24; margin-top: 0;'>❌ Erro de Conexão</h4>\n";
    echo "<p style='color: #721c24;'>Erro: " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
table { margin: 10px 0; font-size: 14px; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; font-weight: bold; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
